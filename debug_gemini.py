#!/usr/bin/env python3
"""
Debug script for Gemini API issues
Tests basic Gemini functionality and JSON response parsing
"""

import os
import json
import asyncio
import google.generativeai as genai
from dotenv import load_dotenv

load_dotenv()

async def test_basic_gemini():
    """Test basic Gemini API functionality"""
    print("Testing basic Gemini API...")
    
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ GEMINI_API_KEY not found in environment")
        return False
    
    try:
        genai.configure(api_key=api_key)
        
        # Test with simple generation config
        generation_config = genai.types.GenerationConfig(
            temperature=0.3,
            max_output_tokens=1000,
            top_p=0.8,
            top_k=40,
        )
        
        model = genai.GenerativeModel(
            model_name="gemini-1.5-pro",
            generation_config=generation_config,
            safety_settings=[
                {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}
            ]
        )
        
        # Simple test
        response = await asyncio.to_thread(
            model.generate_content,
            "Hello! Please respond with exactly: {'status': 'working', 'message': 'API is functional'}"
        )
        
        print(f"✅ Basic response received: {response.text[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ Basic Gemini test failed: {e}")
        return False

async def test_json_response():
    """Test JSON response generation"""
    print("\nTesting JSON response generation...")
    
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ GEMINI_API_KEY not found")
        return False
    
    try:
        genai.configure(api_key=api_key)
        
        generation_config = genai.types.GenerationConfig(
            temperature=0.1,  # Very low temperature for consistent JSON
            max_output_tokens=500,
            top_p=0.8,
        )
        
        model = genai.GenerativeModel(
            model_name="gemini-1.5-pro",
            generation_config=generation_config,
            safety_settings=[
                {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}
            ]
        )
        
        prompt = """Return valid JSON only with this exact structure:
{
    "test_type": "unit_test",
    "complexity": "medium",
    "areas": ["alu", "arithmetic"],
    "time": 30.0,
    "summary": "Basic ALU test"
}

Respond with JSON only, no other text."""
        
        response = await asyncio.to_thread(model.generate_content, prompt)
        
        if hasattr(response, 'text') and response.text:
            text = response.text.strip()
            print(f"Raw response: {text}")
            
            # Try to parse JSON
            try:
                parsed = json.loads(text)
                print(f"✅ Valid JSON parsed: {parsed}")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ JSON parsing failed: {e}")
                print(f"Response text: {text}")
                return False
        else:
            print("❌ No text in response")
            if hasattr(response, 'candidates'):
                for i, candidate in enumerate(response.candidates):
                    print(f"Candidate {i} finish_reason: {candidate.finish_reason}")
            return False
            
    except Exception as e:
        print(f"❌ JSON test failed: {e}")
        return False

async def test_verilog_analysis():
    """Test Verilog code analysis"""
    print("\nTesting Verilog analysis...")
    
    verilog_code = """
module test_alu_basic;
    parameter DATA_WIDTH = 32;
    logic [DATA_WIDTH-1:0] a, b, result;
    logic [3:0] opcode;
    
    alu dut (.a(a), .b(b), .opcode(opcode), .result(result));
    
    initial begin
        opcode = 4'b0000; // ADD
        a = 32'h12345678;
        b = 32'h87654321;
        #10;
        $display("Result: %h", result);
        $finish;
    end
endmodule
"""
    
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ GEMINI_API_KEY not found")
        return False
    
    try:
        genai.configure(api_key=api_key)
        
        generation_config = genai.types.GenerationConfig(
            temperature=0.2,
            max_output_tokens=800,
        )
        
        model = genai.GenerativeModel(
            model_name="gemini-1.5-pro",
            generation_config=generation_config,
            safety_settings=[
                {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}
            ]
        )
        
        prompt = f"""Analyze this Verilog test and return JSON only:

{verilog_code}

Return JSON with these keys:
{{
    "test_type": "unit_test",
    "complexity_level": "low",
    "functional_areas": ["alu"],
    "estimated_execution_time": 10.0,
    "summary": "Tests ALU addition operation"
}}

JSON only, no explanations."""
        
        response = await asyncio.to_thread(model.generate_content, prompt)
        
        if hasattr(response, 'text') and response.text:
            text = response.text.strip()
            
            # Clean up response
            if text.startswith('```json'):
                text = text.replace('```json', '').replace('```', '').strip()
            elif text.startswith('```'):
                text = text.replace('```', '').strip()
            
            print(f"Cleaned response: {text}")
            
            try:
                parsed = json.loads(text)
                print(f"✅ Verilog analysis successful: {parsed}")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ JSON parsing failed: {e}")
                return False
        else:
            print("❌ No text in response")
            return False
            
    except Exception as e:
        print(f"❌ Verilog analysis failed: {e}")
        return False

async def main():
    """Run all debug tests"""
    print("=" * 60)
    print("Gemini API Debug Tests")
    print("=" * 60)
    
    tests = [
        ("Basic Gemini API", test_basic_gemini),
        ("JSON Response", test_json_response),
        ("Verilog Analysis", test_verilog_analysis),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"Running: {test_name}")
        print(f"{'-' * 40}")
        
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'=' * 60}")
    print("DEBUG TEST SUMMARY")
    print(f"{'=' * 60}")
    
    passed = sum(1 for _, result in results if result)
    failed = len(results) - passed
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        symbol = "✅" if result else "❌"
        print(f"{symbol} {test_name}: {status}")
    
    print(f"\nPassed: {passed}/{len(results)}")
    
    if failed == 0:
        print("\n🎉 All debug tests passed! Gemini API is working correctly.")
    else:
        print(f"\n⚠️ {failed} test(s) failed. Check your API key and network connection.")

if __name__ == "__main__":
    asyncio.run(main())
