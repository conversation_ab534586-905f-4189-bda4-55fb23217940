# GenAI-Driven Hardware Verification Test Optimization

An AI-powered system for optimizing hardware verification test suites using Google Gemini 2.5 Pro. This tool helps reduce test execution time while maintaining or improving bug detection effectiveness and coverage.

## Features

- **AI-Driven Test Analysis**: Uses Google Gemini 2.5 Pro to analyze test cases and extract detailed characteristics
- **Redundancy Detection**: Identifies and eliminates redundant test cases using semantic analysis
- **Intelligent Prioritization**: Ranks tests by bug detection potential and coverage value
- **Coverage Gap Analysis**: Identifies missing coverage areas and suggests new tests
- **EDA Tool Integration**: Generates scripts for VCS, Questa, and Xcelium simulators
- **Comprehensive Reporting**: Provides detailed optimization reports and recommendations

## Installation

1. **Clone the repository**:

   ```bash
   git clone <repository-url>
   cd BTP
   ```

2. **Create and activate virtual environment**:

   ```bash
   python -m venv venv
   # On Windows:
   venv\Scripts\activate
   # On Linux/Mac:
   source venv/bin/activate
   ```

3. **Install dependencies**:

   ```bash
   pip install -r requirements.txt
   ```

4. **Set up Google Gemini API**:
   - Get your API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Set the environment variable:
     ```bash
     # On Windows:
     set GEMINI_API_KEY=your_api_key_here
     # On Linux/Mac:
     export GEMINI_API_KEY=your_api_key_here
     ```

## Quick Start

1. **Prepare your test suite**: Place your Verilog test files in a directory structure
2. **Configure the system**: Edit `genai_config.json` to match your requirements
3. **Run optimization**:
   ```bash
   python btp.py
   ```

## Configuration

Edit `genai_config.json` to customize:

- **Model settings**: Choose Gemini model and parameters
- **Optimization thresholds**: Set similarity and coverage thresholds
- **EDA tool preferences**: Configure for your simulation environment
- **Output options**: Control report generation and formats

## Usage Examples

### Basic Optimization

```python
import asyncio
from btp import GenAIVerificationOptimizer

async def main():
    optimizer = GenAIVerificationOptimizer("genai_config.json")
    results = await optimizer.optimize_test_suite(
        test_suite_path="./test_suite/",
        output_path="./optimized_results/"
    )
    print(f"Reduced from {results['original_count']} to {results['selected_count']} tests")

asyncio.run(main())
```

### Custom Configuration

```python
config = {
    "model_name": "gemini-1.5-pro",
    "max_selected_tests": 1000,
    "similarity_threshold": 0.9,
    "batch_size": 10
}

optimizer = GenAIVerificationOptimizer()
optimizer.config.update(config)
```

## Test Suite Structure

Organize your test files in a directory structure:

```
test_suite/
├── unit_tests/
│   ├── test_alu_basic.sv
│   ├── test_memory_basic.sv
│   └── ...
├── integration_tests/
│   ├── test_cpu_integration.sv
│   └── ...
└── stress_tests/
    ├── test_alu_stress.sv
    └── ...
```

## Output Structure

The optimization generates:

```
optimized_results/
├── optimization_report.md          # Comprehensive analysis report
├── selected_tests.txt              # Simple list of selected tests
├── detailed_test_list.json         # Detailed test metadata
├── vcs_run_script.sh               # VCS simulation script
├── questa_run_script.sh            # Questa simulation script
├── xcelium_run_script.sh           # Xcelium simulation script
└── generated_eda_scripts.txt       # All generated scripts
```

## Key Components

### GenAIVerificationOptimizer

Main orchestrator that coordinates the optimization pipeline.

### GenAITestAnalyzer

Analyzes individual test cases to extract characteristics like complexity, bug detection potential, and coverage areas.

### GenAIRedundancyDetector

Identifies redundant tests using semantic similarity analysis.

### GenAITestPrioritizer

Ranks tests based on multiple criteria including bug detection potential and coverage value.

### GenAICoverageAnalyzer

Identifies coverage gaps and suggests tests to fill them.

## Optimization Process

1. **Test Loading**: Discovers and parses test files
2. **AI Analysis**: Extracts test characteristics using Gemini
3. **Redundancy Detection**: Groups similar tests and identifies redundancies
4. **Prioritization**: Ranks tests by value and importance
5. **Coverage Analysis**: Identifies gaps and suggests improvements
6. **Selection**: Chooses optimal subset of tests
7. **Report Generation**: Creates comprehensive reports and scripts

## Best Practices

- **Test Organization**: Use clear naming conventions and directory structure
- **Configuration Tuning**: Adjust thresholds based on your test suite characteristics
- **Incremental Optimization**: Start with small test sets to validate configuration
- **Regular Updates**: Re-run optimization as test suite evolves
- **Manual Review**: Always review AI recommendations before implementation

## Troubleshooting

### Common Issues

1. **API Key Issues**:

   - Ensure GEMINI_API_KEY is set correctly
   - Check API key permissions and quotas

2. **Memory Issues**:

   - Reduce batch_size in configuration
   - Process test suite in smaller chunks

3. **Analysis Quality**:
   - Increase temperature for more creative analysis
   - Adjust similarity_threshold for redundancy detection

### Debug Mode

Enable detailed logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:

- Create an issue on GitHub
- Check the troubleshooting section
- Review the configuration documentation

## Acknowledgments

- Google Gemini API for AI capabilities
- Hardware verification community for best practices
- EDA tool vendors for integration support
