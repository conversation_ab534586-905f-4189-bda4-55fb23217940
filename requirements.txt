# Core dependencies for GenAI Verification Test Optimization
google-generativeai>=0.3.0
python-dotenv>=1.0.0
asyncio-throttle>=1.0.2
aiofiles>=23.0.0

# Data processing and analysis
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.10.0

# JSON and configuration handling
jsonschema>=4.17.0
pyyaml>=6.0

# Logging and monitoring
structlog>=23.0.0
colorlog>=6.7.0

# File and path utilities
pathlib2>=2.3.7
watchdog>=3.0.0

# Async utilities
asyncio>=3.4.3
aiohttp>=3.8.0

# Optional: For advanced text processing
nltk>=3.8.0
textdistance>=4.5.0

# Optional: For visualization and reporting
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.14.0

# Optional: For parallel processing
multiprocessing-logging>=0.3.4
concurrent-futures>=3.1.1

# Development and testing
pytest>=7.3.0
pytest-asyncio>=0.21.0
pytest-mock>=3.10.0
black>=23.0.0
flake8>=6.0.0

# Documentation
sphinx>=6.0.0
sphinx-rtd-theme>=1.2.0
