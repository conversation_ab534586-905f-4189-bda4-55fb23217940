#!/usr/bin/env python3
"""
Installation and setup script for GenAI Verification Test Optimization
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"📦 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 3.8+ required, found {version.major}.{version.minor}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    # Core dependencies that are essential
    core_deps = [
        "google-generativeai>=0.3.0",
        "python-dotenv>=1.0.0",
    ]
    
    # Install core dependencies first
    for dep in core_deps:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            return False
    
    # Try to install other dependencies, but don't fail if they don't work
    optional_deps = [
        "pandas>=2.0.0",
        "numpy>=1.24.0",
        "jsonschema>=4.17.0",
    ]
    
    for dep in optional_deps:
        run_command(f"pip install {dep}", f"Installing optional {dep}")
    
    return True

def setup_environment():
    """Set up environment files"""
    print("🔧 Setting up environment...")
    
    # Create .env file from template if it doesn't exist
    if not os.path.exists('.env'):
        if os.path.exists('.env.template'):
            shutil.copy('.env.template', '.env')
            print("✅ Created .env file from template")
            print("⚠️  Please edit .env file and add your GEMINI_API_KEY")
        else:
            # Create basic .env file
            with open('.env', 'w') as f:
                f.write("# Google Gemini API Key\n")
                f.write("GEMINI_API_KEY=your_api_key_here\n")
            print("✅ Created basic .env file")
            print("⚠️  Please edit .env file and add your GEMINI_API_KEY")
    else:
        print("✅ .env file already exists")
    
    # Create output directories
    os.makedirs('optimized_results', exist_ok=True)
    os.makedirs('test_output', exist_ok=True)
    print("✅ Created output directories")
    
    return True

def verify_installation():
    """Verify the installation"""
    print("🔍 Verifying installation...")
    
    try:
        import google.generativeai as genai
        print("✅ Google Generative AI imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import google.generativeai: {e}")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ python-dotenv imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import dotenv: {e}")
        return False
    
    # Check if main module can be imported
    try:
        sys.path.insert(0, '.')
        from btp import GenAIVerificationOptimizer
        print("✅ Main optimization module imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import main module: {e}")
        return False
    
    return True

def main():
    """Main installation process"""
    print("=" * 60)
    print("GenAI Verification Test Optimization - Installation")
    print("=" * 60)
    
    steps = [
        ("Check Python Version", check_python_version),
        ("Install Dependencies", install_dependencies),
        ("Setup Environment", setup_environment),
        ("Verify Installation", verify_installation),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{'-' * 40}")
        print(f"Step: {step_name}")
        print(f"{'-' * 40}")
        
        if not step_func():
            print(f"\n❌ Installation failed at step: {step_name}")
            print("\nTroubleshooting:")
            print("1. Make sure you have Python 3.8+ installed")
            print("2. Try running: pip install --upgrade pip")
            print("3. Check your internet connection")
            print("4. Try installing dependencies manually")
            return False
    
    print(f"\n{'=' * 60}")
    print("🎉 INSTALLATION COMPLETED SUCCESSFULLY!")
    print(f"{'=' * 60}")
    
    print("\nNext steps:")
    print("1. Get your Gemini API key from: https://makersuite.google.com/app/apikey")
    print("2. Edit the .env file and add your API key:")
    print("   GEMINI_API_KEY=your_actual_api_key_here")
    print("3. Test the installation:")
    print("   python debug_gemini.py")
    print("4. Run the optimization:")
    print("   python btp.py")
    
    print("\nFiles created:")
    print("- .env (add your API key here)")
    print("- optimized_results/ (output directory)")
    print("- test_output/ (temporary output)")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nInstallation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\nUnexpected error during installation: {e}")
        sys.exit(1)
