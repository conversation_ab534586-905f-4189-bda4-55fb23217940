{"total_tests": 6, "selected_tests": 6, "rejected_tests": 0, "selection_ratio": 1.0, "selected_test_names": ["test_alu_basic.sv", "test_alu_corner_cases.sv", "test_alu_redundant.sv", "test_alu_stress.sv", "test_interrupt_controller.sv", "test_memory_interface.sv"], "rejected_test_names": [], "detailed_results": [{"file_name": "test_alu_basic.sv", "decision": "SELECT", "reasoning": "no_errors; high_overall_value", "error_count": 0, "ai_analysis": {"test_type": "unit_test", "complexity": "medium", "bug_detection_potential": "medium", "execution_time_estimate": 30.0, "unique_coverage": "medium", "maintainability": "high", "overall_value": "high"}}, {"file_name": "test_alu_corner_cases.sv", "decision": "SELECT", "reasoning": "no_errors; high_bug_detection_potential; high_unique_coverage; high_overall_value", "error_count": 0, "ai_analysis": {"test_type": "corner_case", "complexity": "medium", "bug_detection_potential": "high", "execution_time_estimate": 30.0, "unique_coverage": "high", "maintainability": "high", "overall_value": "high"}}, {"file_name": "test_alu_redundant.sv", "decision": "SELECT", "reasoning": "no_errors", "error_count": 0, "ai_analysis": {"test_type": "unit_test", "complexity": "low", "bug_detection_potential": "low", "execution_time_estimate": 10.0, "unique_coverage": "low", "maintainability": "high", "overall_value": "low"}}, {"file_name": "test_alu_stress.sv", "decision": "SELECT", "reasoning": "no_errors; high_bug_detection_potential; high_unique_coverage; high_overall_value", "error_count": 0, "ai_analysis": {"test_type": "stress_test", "complexity": "high", "bug_detection_potential": "high", "execution_time_estimate": 30.0, "unique_coverage": "high", "maintainability": "medium", "overall_value": "high"}}, {"file_name": "test_interrupt_controller.sv", "decision": "SELECT", "reasoning": "no_errors; high_overall_value", "error_count": 0, "ai_analysis": {"test_type": "integration_test", "complexity": "medium", "bug_detection_potential": "medium", "execution_time_estimate": 30.0, "unique_coverage": "medium", "maintainability": "medium", "overall_value": "high"}}, {"file_name": "test_memory_interface.sv", "decision": "SELECT", "reasoning": "no_errors; high_bug_detection_potential; high_unique_coverage; high_overall_value", "error_count": 0, "ai_analysis": {"test_type": "integration_test", "complexity": "medium", "bug_detection_potential": "high", "execution_time_estimate": 30.0, "unique_coverage": "high", "maintainability": "high", "overall_value": "high"}}]}