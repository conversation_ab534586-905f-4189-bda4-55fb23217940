// Interrupt Controller Test - Integration Test
// Tests interrupt handling and priority mechanisms
module test_interrupt_controller;

    // Test parameters
    parameter NUM_INTERRUPTS = 16;
    parameter PRIORITY_LEVELS = 4;
    
    // DUT interface
    logic clk, reset;
    logic [NUM_INTERRUPTS-1:0] interrupt_requests;
    logic [NUM_INTERRUPTS-1:0] interrupt_enables;
    logic [10:0] current_interrupt;
    logic interrupt_active;
    logic interrupt_ack;
    logic [1:0] priority_level;
    
    // Interrupt controller instance
    interrupt_controller #(
        .NUM_INTERRUPTS(NUM_INTERRUPTS),
        .PRIORITY_LEVELS(PRIORITY_LEVELS)
    ) dut (
        .clk(clk),
        .reset(reset),
        .interrupt_requests(interrupt_requests),
        .interrupt_enables(interrupt_enables),
        .current_interrupt(current_interrupt),
        .interrupt_active(interrupt_active),
        .interrupt_ack(interrupt_ack),
        .priority_level(priority_level)
    );
    
    // Clock generation
    always #5 clk = ~clk;
    
    // Test sequence
    initial begin
        // Initialize
        clk = 0;
        reset = 1;
        interrupt_requests = 0;
        interrupt_enables = 0;
        interrupt_ack = 0;
        #20 reset = 0;
        
        // Test basic interrupt handling
        test_basic_interrupts();
        
        // Test interrupt priorities
        test_interrupt_priorities();
        
        // Test interrupt masking
        test_interrupt_masking();
        
        // Test simultaneous interrupts
        test_simultaneous_interrupts();
        
        // Test interrupt nesting
        test_interrupt_nesting();
        
        $display("Interrupt Controller Test Completed");
        $finish;
    end
    
    // Test basic interrupt functionality
    task test_basic_interrupts();
        begin
            $display("Testing basic interrupt handling...");
            
            // Enable all interrupts
            interrupt_enables = {NUM_INTERRUPTS{1'b1}};
            @(posedge clk);
            
            // Test each interrupt individually
            for (int i = 0; i < NUM_INTERRUPTS; i++) begin
                // Assert interrupt request
                interrupt_requests[i] = 1'b1;
                @(posedge clk);
                
                // Check if interrupt is active
                wait(interrupt_active);
                if (current_interrupt !== i) begin
                    $error("Wrong interrupt active: expected %d, got %d", i, current_interrupt);
                end
                
                // Acknowledge interrupt
                interrupt_ack = 1'b1;
                @(posedge clk);
                interrupt_ack = 1'b0;
                interrupt_requests[i] = 1'b0;
                @(posedge clk);
                
                // Wait for interrupt to clear
                wait(!interrupt_active);
            end
            
            $display("Basic interrupt tests passed");
        end
    endtask
    
    // Test interrupt priority handling
    task test_interrupt_priorities();
        begin
            $display("Testing interrupt priorities...");
            
            interrupt_enables = {NUM_INTERRUPTS{1'b1}};
            
            // Test high priority interrupt preempting low priority
            interrupt_requests[0] = 1'b1; // Low priority
            @(posedge clk);
            wait(interrupt_active);
            
            // Assert high priority interrupt
            interrupt_requests[15] = 1'b1; // High priority
            @(posedge clk);
            
            // High priority should be active
            if (current_interrupt !== 15) begin
                $error("High priority interrupt not active: expected 15, got %d", current_interrupt);
            end
            
            // Acknowledge high priority
            interrupt_ack = 1'b1;
            @(posedge clk);
            interrupt_ack = 1'b0;
            interrupt_requests[15] = 1'b0;
            @(posedge clk);
            
            // Low priority should resume
            wait(interrupt_active);
            if (current_interrupt !== 0) begin
                $error("Low priority interrupt not resumed: expected 0, got %d", current_interrupt);
            end
            
            // Clean up
            interrupt_ack = 1'b1;
            @(posedge clk);
            interrupt_ack = 1'b0;
            interrupt_requests[0] = 1'b0;
            @(posedge clk);
            
            $display("Priority tests passed");
        end
    endtask
    
    // Test interrupt masking
    task test_interrupt_masking();
        begin
            $display("Testing interrupt masking...");
            
            // Disable all interrupts
            interrupt_enables = 0;
            @(posedge clk);
            
            // Assert interrupt request
            interrupt_requests[5] = 1'b1;
            @(posedge clk);
            
            // Should not be active (masked)
            repeat(5) @(posedge clk);
            if (interrupt_active) begin
                $error("Masked interrupt became active");
            end
            
            // Enable the interrupt
            interrupt_enables[5] = 1'b1;
            @(posedge clk);
            
            // Should now be active
            wait(interrupt_active);
            if (current_interrupt !== 5) begin
                $error("Unmasked interrupt not active");
            end
            
            // Clean up
            interrupt_ack = 1'b1;
            @(posedge clk);
            interrupt_ack = 1'b0;
            interrupt_requests[5] = 1'b0;
            @(posedge clk);
            
            $display("Masking tests passed");
        end
    endtask
    
    // Test simultaneous interrupts
    task test_simultaneous_interrupts();
        begin
            $display("Testing simultaneous interrupts...");
            
            interrupt_enables = {NUM_INTERRUPTS{1'b1}};
            
            // Assert multiple interrupts simultaneously
            interrupt_requests[3:0] = 4'b1111;
            @(posedge clk);
            
            // Highest priority should be active
            wait(interrupt_active);
            if (current_interrupt !== 3) begin
                $error("Wrong interrupt priority: expected 3, got %d", current_interrupt);
            end
            
            // Service interrupts in priority order
            for (int i = 3; i >= 0; i--) begin
                if (current_interrupt !== i) begin
                    $error("Wrong interrupt order: expected %d, got %d", i, current_interrupt);
                end
                
                interrupt_ack = 1'b1;
                @(posedge clk);
                interrupt_ack = 1'b0;
                interrupt_requests[i] = 1'b0;
                @(posedge clk);
                
                if (i > 0) wait(interrupt_active);
            end
            
            $display("Simultaneous interrupt tests passed");
        end
    endtask
    
    // Test interrupt nesting
    task test_interrupt_nesting();
        begin
            $display("Testing interrupt nesting...");
            
            interrupt_enables = {NUM_INTERRUPTS{1'b1}};
            
            // Start with low priority interrupt
            interrupt_requests[1] = 1'b1;
            @(posedge clk);
            wait(interrupt_active);
            
            // Nest higher priority interrupt
            interrupt_requests[10] = 1'b1;
            @(posedge clk);
            
            // Higher priority should preempt
            if (current_interrupt !== 10) begin
                $error("Interrupt nesting failed: expected 10, got %d", current_interrupt);
            end
            
            // Service nested interrupt
            interrupt_ack = 1'b1;
            @(posedge clk);
            interrupt_ack = 1'b0;
            interrupt_requests[10] = 1'b0;
            @(posedge clk);
            
            // Original interrupt should resume
            wait(interrupt_active);
            if (current_interrupt !== 1) begin
                $error("Interrupt return failed: expected 1, got %d", current_interrupt);
            end
            
            // Clean up
            interrupt_ack = 1'b1;
            @(posedge clk);
            interrupt_ack = 1'b0;
            interrupt_requests[1] = 1'b0;
            @(posedge clk);
            
            $display("Nesting tests passed");
        end
    endtask

endmodule
