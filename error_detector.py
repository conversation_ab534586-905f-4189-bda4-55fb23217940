import re
import os
def check_uninitialized_signals(code):
    """
    Checks for signals that are read before they are written to in a sequential block.
    This is a simplified check and might not catch all cases.
    It looks for 'reg' declarations and checks if they are used on the RHS of a non-blocking assignment
    before being assigned a value within a sequential always block.
    """
    uninitialized_signals = []
    # Find all sequential always blocks
    always_blocks = re.finditer(r'always\s*@\s*\(\s*(posedge|negedge)\s+\w+\s*\)(.*?)end', code, re.DOTALL)
    
    # Find all registered signals
    regs = set(re.findall(r'\breg\s*(?:\[.?\])?\s(\w+)\s*;', code))
    
    for block in always_blocks:
        block_content = block.group(2)
        # Signals assigned to in this block
        assigned_in_block = set(re.findall(r'\b(\w+)\b\s*<=', block_content))
        
        # Signals read on the RHS of non-blocking assignments
        read_in_block = set(re.findall(r'<=\s*.*?\b(\w+)\b', block_content))

        # An uninitialized read happens if a reg is read but never assigned in this block,
        # and it's not a port (this check is simplified and assumes inputs are not regs)
        potentially_uninitialized = (read_in_block - assigned_in_block) & regs
        
        if potentially_uninitialized:
            line_number = code.count('\n', 0, block.start()) + 1
            for sig in potentially_uninitialized:
                 uninitialized_signals.append(f"Line {line_number}: Signal '{sig}' may be read before being initialized.")

    return uninitialized_signals

def check_multiple_drivers(code):
    """
    Checks for multiple drivers on the same net.
    Looks for the same signal on the left-hand side of multiple 'assign' statements
    or in multiple always blocks.
    """
    drivers = {}
    # Find all assignments
    assignments = re.findall(r'assign\s+(.?)\s=', code, re.DOTALL)
    for assignment in assignments:
        # Clean up vector part like [7:0]
        net = re.sub(r'\[.*?\]', '', assignment).strip()
        if net in drivers:
            drivers[net] += 1
        else:
            drivers[net] = 1

    # Find all signals assigned in always blocks
    always_blocks = re.findall(r'always\s*@\s*\(.?\)\s*begin(.?)end', code, re.DOTALL)
    for block in always_blocks:
        block_assignments = re.findall(r'\b(\w+)\b\s*<*=', block)
        for signal in set(block_assignments):
             if signal in drivers:
                drivers[signal] += 1
             else:
                drivers[signal] = 1


    multiple_drivers = [net for net, count in drivers.items() if count > 1]
    return multiple_drivers

def check_division_by_zero(code):
    """
    Checks for division by a constant zero.
    """
    division_by_zero_found = []
    # Matches expressions like '... / 0' or '... / 0;'
    matches = re.finditer(r'/\s*0\b', code)
    for match in matches:
        line_number = code.count('\n', 0, match.start()) + 1
        division_by_zero_found.append(f"Line {line_number}: Division by zero.")
    return division_by_zero_found

def check_out_of_range_indexing(code):
    """
    Checks for out-of-range indexing for declared vectors.
    This is a simplified check.
    """
    out_of_range_access = []
    # Find all vector declarations like 'reg [7:0] my_vec;'
    declarations = re.findall(r'\b(reg|wire)\s*\[(\d+):(\d+)\]\s*(\w+);', code)
    vectors = {name: (int(msb), int(lsb)) for _, msb, lsb, name in declarations}

    for name, (msb, lsb) in vectors.items():
        # Find all accesses to this vector
        accesses = re.finditer(r'\b' + name + r'\[(\d+)\]', code)
        for access in accesses:
            index = int(access.group(1))
            if not (lsb <= index <= msb or msb <= index <= lsb):
                 line_number = code.count('\n', 0, access.start()) + 1
                 out_of_range_access.append(f"Line {line_number}: Out-of-range access for '{name}'. Index {index} is outside the range [{msb}:{lsb}].")

    return out_of_range_access


def check_sensitivity_list(code):
    """
    Checks for missing signals in sensitivity lists of combinational always blocks.
    """
    missing_sensitivity = []
    # Find all combinational always blocks
    always_blocks = re.finditer(r'always\s*@\s*\((.?)\)\s(begin)?(.*?)(end)?', code, re.DOTALL)
    for block_match in always_blocks:
        sensitivity_list_str = block_match.group(1)
        block_content = block_match.group(3)

        # Only check for combinational logic (ignore sequential blocks)
        if '@*' in sensitivity_list_str or 'posedge' in sensitivity_list_str or 'negedge' in sensitivity_list_str:
            continue

        sensitivity_list = set(re.findall(r'\b\w+\b', sensitivity_list_str))

        # Find all signals read in the block (RHS of assignments)
        rh_sides = re.findall(r'=\s*(.*?);', block_content, re.DOTALL)
        read_signals = set()
        for side in rh_sides:
            # Find all identifiers on the right hand side
            signals = re.findall(r'\b\w+\b', side)
            for signal in signals:
                # a signal isn't a number
                if not signal.isdigit():
                    read_signals.add(signal)

        missing = read_signals - sensitivity_list
        if missing:
            line_number = code.count('\n', 0, block_match.start()) + 1
            missing_sensitivity.append(f"Line {line_number}: Missing signals in sensitivity list: {', '.join(missing)}")

    return missing_sensitivity


def check_infinite_loops(code):
    """
    Checks for potential infinite loops in always @* blocks.
    A very simple check: looks for always @* without any assignments.
    This is a heuristic and can have false positives/negatives.
    """
    infinite_loops = []
    always_star_blocks = re.finditer(r'always\s*@\s*\(\s*\\s\)\s*(begin)?(.*?)(end)?', code, re.DOTALL)
    for match in always_star_blocks:
        block_content = match.group(2)
        # If there are no assignments, it might be an infinite loop if it contains other constructs
        # or simply a latch if it's empty. A better check would be for combinational loops.
        if '<=' not in block_content and '=' not in block_content:
            line_number = code.count('\n', 0, match.start()) + 1
            # This is a weak check. A latch is more likely.
            infinite_loops.append(f"Line {line_number}: Potential latch or incomplete combinational logic in always @* block. This can cause simulation mismatches.")
    return infinite_loops


def analyze_verilog(file_path):
    """
    Analyzes a Verilog file for potential issues.
    """
    with open(file_path, 'r') as f:
        code = f.read()

    print(f"Analyzing {file_path}...")

    issues = []
    issues.extend(check_uninitialized_signals(code))
    issues.extend(check_multiple_drivers(code))
    issues.extend(check_division_by_zero(code))
    issues.extend(check_out_of_range_indexing(code))
    issues.extend(check_sensitivity_list(code))
    issues.extend(check_infinite_loops(code))

    if issues:
        print("\nPotential issues found:")
        for issue in issues:
            print(f"- {issue}")
    else:
        print("\nNo major issues found by this basic analyzer.")

if __name__ == "__main__":
    # if len(sys.argv) != 2:
    #     print("Usage: python analyzer.py <path_to_verilog_file>")
    #     sys.exit(1)
    
    folder_path = "C:/Users/<USER>/Desktop/BTP/test_suite"
    for item in os.listdir(folder_path):
        file_path = os.path.join(folder_path,item)
        analyze_verilog(file_path)