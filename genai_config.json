{"llm_provider": "google", "model_name": "gemini-2.5-pro", "max_tokens": 8000, "temperature": 0.2, "batch_size": 15, "similarity_threshold": 0.85, "max_selected_tests": 5000, "analysis_depth": "detailed", "prompt_templates": {"test_analysis": "templates/test_analysis_prompt.txt", "redundancy_check": "templates/redundancy_check_prompt.txt", "prioritization": "templates/prioritization_prompt.txt"}, "optimization_settings": {"enable_redundancy_detection": true, "enable_coverage_analysis": true, "enable_test_prioritization": true, "parallel_processing": true, "max_parallel_batches": 4}, "coverage_settings": {"required_functional_areas": ["data_path", "control_logic", "interface_protocols", "error_handling", "reset_initialization", "interrupt_handling", "memory_operations", "power_management"], "required_test_types": ["unit_test", "integration_test", "regression_test", "stress_test", "corner_case", "negative_test"], "minimum_coverage_threshold": 0.8}, "prioritization_weights": {"bug_detection_potential": 0.4, "coverage_value": 0.25, "risk_mitigation": 0.2, "uniqueness": 0.1, "efficiency": 0.05}, "eda_tool_settings": {"supported_tools": ["vcs", "questa", "xcelium"], "default_tool": "vcs", "parallel_jobs": 8, "coverage_options": ["-cm", "line+cond+fsm+tgl+branch"], "simulation_options": ["+ntb_random_seed_automatic"]}, "output_settings": {"generate_reports": true, "generate_scripts": true, "report_format": "markdown", "include_detailed_analysis": true, "save_intermediate_results": true}}