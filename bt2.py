

import os
import json
import asyncio
import tempfile
import re
import sys
from typing import List, Dict, Any, Optional, Literal
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# LangGraph
from langgraph.graph import StateGraph, START, END

# LLM (Google Gemini wrapper)
try:
    from langchain_core.messages import HumanMessage
    from langchain_google_genai import ChatGoogleGenerativeAI
except Exception as e:
    # If imports fail, raise because we explicitly do NOT support fallback
    raise ImportError(
        "Required LLM packages not available. Install langchain_google_genai and langchain_core "
        "or ensure they are importable. Aborting since this script only supports structured LLM outputs."
    ) from e

load_dotenv()
GOOGLE_API_KEY = os.getenv("GEMINI_API_KEY")
if not GOOGLE_API_KEY:
    raise RuntimeError("GOOGLE_API_KEY not set in environment. This script requires a configured Google API key.")

# ----------------- Pydantic models -----------------


class TestFile(BaseModel):
    file_path: str
    file_name: str
    content: str = ""
    error_count: int = 0
    error_details: List[str] = Field(default_factory=list)
    ai_analysis: Dict[str, Any] = Field(default_factory=dict)
    decision: str = "PENDING"  # SELECT/REJECT/PENDING
    reasoning: str = ""


class WorkflowState(BaseModel):
    test_suite_path: str = "./test_suite"
    test_files: List[TestFile] = Field(default_factory=list)
    current_index: int = 0
    total_files: int = 0
    selected_tests: List[str] = Field(default_factory=list)
    rejected_tests: List[str] = Field(default_factory=list)


class AnalysisModel(BaseModel):
    """
    Expected structured response from the LLM.
    Uses Literal types so values are validated strictly.
    """
    test_type: Optional[Literal["unit_test", "integration_test", "stress_test", "corner_case"]] = None
    complexity: Optional[Literal["low", "medium", "high"]] = None
    bug_detection_potential: Optional[Literal["low", "medium", "high"]] = None
    execution_time_estimate: Optional[float] = None
    unique_coverage: Optional[Literal["low", "medium", "high"]] = None
    maintainability: Optional[Literal["low", "medium", "high"]] = None
    overall_value: Optional[Literal["low", "medium", "high"]] = None

    class Config:
        extra = "forbid"  # enforce only expected keys (strict)


# ----------------- Main optimizer -----------------


class TestOptimizerLangGraphStructured:
    """
    LangGraph-based optimizer that strictly uses LLM.with_structured_output(AnalysisModel).
    No fallback text parsing is included by design.
    """

    def __init__(self):
        self.llm = self._build_llm()
        # Build structured wrapper — will raise if not supported
        if not hasattr(self.llm, "with_structured_output"):
            raise RuntimeError("LLM instance does not support with_structured_output — aborting (no fallback).")

        # Create structured LLM that returns AnalysisModel instances
        try:
            self.structured_llm = self.llm.with_structured_output(AnalysisModel)
        except Exception as e:
            raise RuntimeError("Failed to create structured LLM via with_structured_output") from e

        self.workflow = self._create_workflow()

    def _build_llm(self):
        """
        Build ChatGoogleGenerativeAI LLM instance.
        """
        try:
            return ChatGoogleGenerativeAI(
                model="gemini-2.5-flash",
                temperature=0.3,
                max_tokens=2048,
                timeout=None,
                max_retries=2,
                api_key=GOOGLE_API_KEY,
            )
        except Exception as e:
            raise RuntimeError("Failed to construct ChatGoogleGenerativeAI instance") from e

    def _create_workflow(self):
        g = StateGraph(WorkflowState)

        # Register nodes (linear)
        g.add_node("load_files", self.load_files_node)
        g.add_node("error_detection", self.error_detection_node)
        g.add_node("ai_analysis", self.ai_analysis_node)  # async node
        g.add_node("decision", self.decision_node)

        g.add_edge(START, "load_files")
        g.add_edge("load_files", "error_detection")
        g.add_edge("error_detection", "ai_analysis")
        g.add_edge("ai_analysis", "decision")
        g.add_edge("decision", END)

        return g.compile()

    # ---------- nodes ----------

    def load_files_node(self, state: WorkflowState) -> WorkflowState:
        path = state.test_suite_path or "./test_suite"
        files: List[TestFile] = []

        if not os.path.exists(path):
            raise FileNotFoundError(f"Test suite path '{path}' does not exist")

        for root, _, filenames in os.walk(path):
            for fname in filenames:
                if fname.endswith((".v", ".sv", ".vh")):
                    fpath = os.path.join(root, fname)
                    with open(fpath, "r", encoding="utf-8", errors="ignore") as f:
                        content = f.read()
                    files.append(TestFile(file_path=fpath, file_name=fname, content=content))

        state.test_files = files
        state.total_files = len(files)
        print(f"Loaded {state.total_files} test file(s) from {path}")
        return state

    def error_detection_node(self, state: WorkflowState) -> WorkflowState:
        print("🔍 Running error detection...")
        try:
            from error_detector import analyze_verilog
        except Exception as e:
            raise ImportError("Could not import error_detector.analyze_verilog; ensure module is on PYTHONPATH") from e

        import io
        from contextlib import redirect_stdout

        for tf in state.test_files:
            with tempfile.NamedTemporaryFile("w", delete=False, suffix=os.path.splitext(tf.file_name)[1]) as tmp:
                tmp.write(tf.content)
                tmp_path = tmp.name

            captured = io.StringIO()
            with redirect_stdout(captured):
                try:
                    analyze_verilog(tmp_path)
                except Exception as inner_e:
                    # Let analyzer's error output be captured and handled below
                    print(f"analyze_verilog raised: {inner_e}")

            out = captured.getvalue()
            try:
                os.remove(tmp_path)
            except Exception:
                pass

            # Heuristic for errors; if your analyzer uses a different pattern adjust this
            errors = [ln.strip() for ln in out.splitlines() if ln.strip().startswith("- Line")]
            tf.error_count = len(errors)
            tf.error_details = errors
            print(f"  {tf.file_name}: {tf.error_count} errors")

        return state

    async def ai_analysis_node(self, state: WorkflowState) -> WorkflowState:
        """
        REQUIRED: uses structured LLM only.
        Calls structured_llm.ainvoke(prompt) and expects AnalysisModel instance (or wrapper exposing it).
        """
        print("🤖 Running AI analysis (structured output only)...")
        # We'll call structured_llm.ainvoke for async; if only sync invoke exists it should still work in many wrappers,
        # but we expect `ainvoke` to be available for the structured wrapper.
        if not hasattr(self.structured_llm, "ainvoke"):
            raise RuntimeError("Structured LLM wrapper does not expose 'ainvoke' — structured-only mode requires async support.")

        for tf in state.test_files:
            prompt = self._build_prompt(tf)
            try:
                # Call the structured LLM asynchronously
                resp = await self.structured_llm.ainvoke(prompt)
                # resp should be an AnalysisModel instance (pydantic BaseModel)
                parsed: Optional[AnalysisModel] = None

                # If resp is a model instance, use it
                if isinstance(resp, AnalysisModel):
                    parsed = resp
                else:
                    # Some implementations return an object with .parsed or .result
                    # (But we are strict — if it's not an AnalysisModel, treat as error)
                    parsed_attr = getattr(resp, "parsed", None) or getattr(resp, "result", None)
                    if isinstance(parsed_attr, AnalysisModel):
                        parsed = parsed_attr

                if parsed is None:
                    raise RuntimeError(f"Structured LLM returned unexpected shape: {type(resp)}")

                # Save structured dict
                tf.ai_analysis = dict(parsed)
                print(f"  {tf.file_name}: AI structured analysis saved")

            except Exception as e:
                # In this strict script we treat structured LLM failures as fatal — no fallback
                raise RuntimeError(f"Structured LLM analysis failed for {tf.file_name}: {e}") from e

        return state

    def decision_node(self, state: WorkflowState) -> WorkflowState:
        print("⚖️ Making selection decisions (using structured AI fields)...")
        for tf in state.test_files:
            reasoning: List[str] = []
            decision = "REJECT"

            # Primary heuristics on error_count
            if tf.error_count == -1:
                reasoning.append("analysis_failed")
                decision = "REJECT"
            elif tf.error_count == 0:
                reasoning.append("no_errors")
                decision = "SELECT"
            elif tf.error_count <= 2:
                reasoning.append(f"few_errors({tf.error_count})")
                decision = "SELECT"
            else:
                reasoning.append(f"many_errors({tf.error_count})")

            # Secondary heuristics from structured AI analysis (if present)
            ai = tf.ai_analysis or {}
            # using strict keys (AnalysisModel) — values will be validated earlier
            if isinstance(ai, dict):
                if ai.get("bug_detection_potential") == "high":
                    reasoning.append("high_bug_detection_potential")
                    decision = "SELECT"
                if ai.get("unique_coverage") == "high":
                    reasoning.append("high_unique_coverage")
                    decision = "SELECT"
                if ai.get("overall_value") == "high":
                    reasoning.append("high_overall_value")
                    decision = "SELECT"
                if ai.get("complexity") == "high" and ai.get("overall_value") == "low":
                    reasoning.append("high_complexity_low_value")
                    decision = "REJECT"

            tf.decision = decision
            tf.reasoning = "; ".join(reasoning)

            if decision == "SELECT":
                state.selected_tests.append(tf.file_name)
            else:
                state.rejected_tests.append(tf.file_name)

            print(f"  {tf.file_name}: {tf.decision} - {tf.reasoning}")

        return state

    # ---------- helpers ----------

    @staticmethod
    def _build_prompt(tf: TestFile) -> str:
        # Keep prompt concise. We ask the model to return EXACTLY the AnalysisModel schema.
        return (
            f"Analyze this Verilog test file for optimization decisions.\n\n"
            f"File: {tf.file_name}\n"
            f"Error Count: {tf.error_count}\n"
            f"Errors Found: {tf.error_details[:3] if tf.error_details else 'None'}\n\n"
            f"Content Preview (first 1000 chars):\n{tf.content[:1000]}\n\n"
            "Return ONLY a single JSON object matching this schema:\n"
            '{'
            '"test_type":"unit_test|integration_test|stress_test|corner_case",'
            '"complexity":"low|medium|high",'
            '"bug_detection_potential":"low|medium|high",'
            '"execution_time_estimate": 30.0,'
            '"unique_coverage":"low|medium|high",'
            '"maintainability":"low|medium|high",'
            '"overall_value":"low|medium|high"'
            '}\n'
        )



    async def optimize_test_suite(self, test_suite_path: str = "./test_suite") -> Dict[str, Any]:
        state = WorkflowState(test_suite_path=test_suite_path)
        print("Starting LangGraph-based optimization (structured LLM only)...")

        # This will execute the defined workflow (which triggers our nodes)
        final_state = await self.workflow.ainvoke(state)
        # print("11--------------------11")
        # print(final_state)
        # print("11--------------------11")
        detailed = [
            {
                "file_name": tf.file_name,
                "decision": tf.decision,
                "reasoning": tf.reasoning,
                "error_count": tf.error_count,
                "ai_analysis": tf.ai_analysis,
            }
            for tf in final_state['test_files']
        ]

        results = {
            "total_tests": final_state['total_files'],
            "selected_tests": len(final_state['selected_tests']),
            "rejected_tests": len(final_state['rejected_tests']),
            "selection_ratio": len(final_state['selected_tests']) / final_state['total_files'] if final_state['total_files'] else 0,
            "selected_test_names": final_state['selected_tests'],
            "rejected_test_names": final_state['rejected_tests'],
            "detailed_results": detailed,
        }

        os.makedirs("optimization_results", exist_ok=True)
        with open("optimization_results/langgraph_structured_results.json", "w", encoding="utf-8") as f:
            json.dump(results, f, indent=2)

        with open("optimization_results/selected_tests.txt", "w", encoding="utf-8") as f:
            for n in final_state['selected_tests']:
                f.write(n + "\n")

        print("\n✅ Optimization complete. Results saved to optimization_results/")
        return results


async def main(argv: List[str]):
    path = argv[1] if len(argv) > 1 else "./test_suite"
    optimizer = TestOptimizerLangGraphStructured()
    results = await optimizer.optimize_test_suite(path)

    # Print summary
    print("\n🎯 Summary:")
    print(f"  Total: {results['total_tests']}")
    print(f"  Selected: {results['selected_tests']}")
    print(f"  Rejected: {results['rejected_tests']}")
    print(f"  Ratio: {results['selection_ratio']:.2%}")


if __name__ == "__main__":
    asyncio.run(main(sys.argv))
