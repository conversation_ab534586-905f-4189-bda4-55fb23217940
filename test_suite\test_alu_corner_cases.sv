// ALU Corner Cases Test - Corner Case Test
// Tests boundary conditions and edge cases
module test_alu_corner_cases;

    // Test parameters
    parameter DATA_WIDTH = 32;
    
    // DUT interface
    logic [DATA_WIDTH-1:0] a, b, result;
    logic [3:0] opcode;
    logic clk, reset;
    logic overflow, zero_flag;
    
    // ALU instance
    alu #(.DATA_WIDTH(DATA_WIDTH)) dut (
        .a(a),
        .b(b),
        //changes are done here
        //.opcode(opcode),
        .result(result),
        .overflow(overflow),
        .zero_flag(zero_flag)
    );
    
    // Clock generation
    always #5 clk = ~clk;
    
    // Test sequence
    initial begin
        // Initialize
        clk = 0;
        reset = 1;
        #10 reset = 0;
        
        // Test overflow conditions
        test_overflow_cases();
        
        // Test zero flag conditions
        test_zero_flag_cases();
        
        // Test maximum values
        test_max_values();
        
        // Test minimum values
        test_min_values();
        
        // Test boundary transitions
        test_boundary_transitions();
        
        $display("ALU Corner Cases Test Completed");
        $finish;
    end
    
    // Test overflow scenarios
    task test_overflow_cases();
        begin
            opcode = 4'b0000; // ADD
            
            // Test positive overflow
            a = 32'h7FFFFFFF; // Max positive
            b = 32'h00000001;
            #10;
            if (!overflow) begin
                $error("Overflow not detected for max positive + 1");
            end
            
            // Test negative overflow
            a = 32'h80000000; // Max negative
            b = 32'hFFFFFFFF; // -1
            #10;
            if (!overflow) begin
                $error("Overflow not detected for max negative - 1");
            end
            
            $display("Overflow tests passed");
        end
    endtask
    
    // Test zero flag scenarios
    task test_zero_flag_cases();
        begin
            // Test zero result from subtraction
            opcode = 4'b0001; // SUB
            a = 32'h12345678;
            b = 32'h12345678;
            #10;
            if (!zero_flag || result !== 0) begin
                $error("Zero flag not set for equal subtraction");
            end
            
            // Test zero result from AND
            opcode = 4'b0010; // AND
            a = 32'hAAAAAAAA;
            b = 32'h55555555;
            #10;
            if (!zero_flag || result !== 0) begin
                $error("Zero flag not set for complementary AND");
            end
            
            $display("Zero flag tests passed");
        end
    endtask
    
    // Test maximum value operations
    task test_max_values();
        begin
            // Test max + max
            opcode = 4'b0000; // ADD
            a = 32'hFFFFFFFF;
            b = 32'hFFFFFFFF;
            #10;
            
            // Test max AND max
            opcode = 4'b0010; // AND
            a = 32'hFFFFFFFF;
            b = 32'hFFFFFFFF;
            #10;
            if (result !== 32'hFFFFFFFF) begin
                $error("Max AND Max failed");
            end
            
            $display("Maximum value tests passed");
        end
    endtask
    
    // Test minimum value operations
    task test_min_values();
        begin
            // Test 0 + 0
            opcode = 4'b0000; // ADD
            a = 32'h00000000;
            b = 32'h00000000;
            #10;
            if (result !== 0 || !zero_flag) begin
                $error("Zero addition failed");
            end
            
            // Test 0 - 0
            opcode = 4'b0001; // SUB
            a = 32'h00000000;
            b = 32'h00000000;
            #10;
            if (result !== 0 || !zero_flag) begin
                $error("Zero subtraction failed");
            end
            
            $display("Minimum value tests passed");
        end
    endtask
    
    // Test boundary transitions
    task test_boundary_transitions();
        begin
            // Test transition from positive to negative
            opcode = 4'b0000; // ADD
            a = 32'h7FFFFFFE; // Max positive - 1
            b = 32'h00000002; // +2
            #10;
            
            // Test transition around zero
            opcode = 4'b0001; // SUB
            a = 32'h00000001; // +1
            b = 32'h00000002; // +2
            #10;
            if (result !== 32'hFFFFFFFF) begin // Should be -1
                $error("Boundary transition failed");
            end
            
            $display("Boundary transition tests passed");
        end
    endtask

endmodule
