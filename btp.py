#!/usr/bin/env python3
"""
Generative AI-Only Verification Test Optimization System
Uses LLMs and generative techniques without traditional ML models
"""

import os
import json
import hashlib
import asyncio
import logging
from typing import List, Dict, Tuple, Any, Optional
from dataclasses import dataclass
from pathlib import Path
import google.generativeai as genai
import re
from collections import defaultdict
import subprocess
from dotenv import load_dotenv
from fix_json_parsing import clean_and_parse_json, get_test_analysis_fallback

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TestCase:
    """Test case data structure"""
    test_id: str
    test_name: str
    file_path: str
    content: str
    parameters: Dict[str, Any]
    execution_time: float = 0.0
    coverage_data: Dict[str, float] = None
    status: str = "UNKNOWN"

class GenAIVerificationOptimizer:
    """Main GenAI-driven test optimization system"""
    
    def __init__(self, config_path: str = "genai_config.json"):
        self.config = self.load_config(config_path)
        self.llm_client = self.initialize_llm_client()
        self.test_analyzer = GenAITestAnalyzer(self.llm_client, self.config)
        self.redundancy_detector = GenAIRedundancyDetector(self.llm_client, self.config)
        self.test_prioritizer = GenAITestPrioritizer(self.llm_client, self.config)
        self.coverage_analyzer = GenAICoverageAnalyzer(self.llm_client, self.config)
        
    def load_config(self, config_path: str) -> Dict:
        """Load configuration for GenAI optimization"""
        default_config = {
            "llm_provider": "google",
            "model_name": "gemini-2.5-pro",
            "max_tokens": 8000,
            "temperature": 0.5,
            "batch_size": 20,
            "similarity_threshold": 0.85,
            "max_selected_tests": 5000,
            "analysis_depth": "detailed",
            "prompt_templates": {
                "test_analysis": "templates/test_analysis_prompt.txt",
                "redundancy_check": "templates/redundancy_check_prompt.txt",
                "prioritization": "templates/prioritization_prompt.txt"
            }
        }
        
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                user_config = json.load(f)
                default_config.update(user_config)
        
        return default_config
    
    def initialize_llm_client(self):
        """Initialize the Gemini client"""
        # Configure Gemini API
        api_key = os.getenv('GEMINI_API_KEY', '')
        if not api_key:
            logger.warning("GEMINI_API_KEY not found in environment variables")

        genai.configure(api_key=api_key)

        # Use Gemini 2.5 Pro configuration
        model_name = self.config.get("model_name", "gemini-2.5-pro")

        # Initialize the model with Gemini 2.5 Pro optimized config (compatible version)
        generation_config = genai.types.GenerationConfig(
            temperature=self.config.get("temperature", 0.2),  # Lower temperature for Gemini 2.5 Pro
            max_output_tokens=self.config.get("max_tokens", 8000),  # Gemini 2.5 Pro supports higher token limits
            top_p=0.95,  # Optimized for Gemini 2.5 Pro
            top_k=64,    # Better for newer model
            candidate_count=1,
            stop_sequences=None
        )

        # Gemini 2.5 Pro safety settings (using standard categories for compatibility)
        safety_settings = [
            {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
            {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
            {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
            {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}
        ]

        model = genai.GenerativeModel(
            model_name=model_name,
            generation_config=generation_config,
            safety_settings=safety_settings,
            system_instruction="You are an expert hardware verification engineer. Always respond with valid JSON format when requested. Be precise and technical in your analysis."
        )
        return model
    
    async def optimize_test_suite(self, test_suite_path: str, output_path: str) -> Dict[str, Any]:
        """Main GenAI-driven optimization pipeline"""
        logger.info(f"Starting GenAI optimization of test suite: {test_suite_path}")
        
        # Step 1: Load and parse test cases
        test_cases = await self.load_test_cases(test_suite_path)
        logger.info(f"Loaded {len(test_cases)} test cases")
        
        # Step 2: GenAI-based test analysis
        analyzed_tests = await self.test_analyzer.analyze_tests(test_cases)
        logger.info("Test analysis completed using GenAI")
        
        # Step 3: GenAI redundancy detection
        unique_tests, redundancy_report = await self.redundancy_detector.detect_redundant_tests(analyzed_tests)
        logger.info(f"Identified {len(analyzed_tests) - len(unique_tests)} redundant tests")
        
        # Step 4: GenAI-based test prioritization
        prioritized_tests = await self.test_prioritizer.prioritize_tests(unique_tests)
        logger.info("Test prioritization completed")
        
        # Step 5: GenAI coverage gap analysis
        coverage_gaps = await self.coverage_analyzer.analyze_coverage_gaps(prioritized_tests)
        
        # Step 6: Select optimal subset
        selected_tests = await self.select_optimal_subset(prioritized_tests, coverage_gaps)
        
        # Step 7: Generate comprehensive reports
        optimization_results = await self.generate_optimization_report(
            test_cases, selected_tests, redundancy_report, coverage_gaps, output_path
        )
        
        logger.info(f"GenAI optimization completed. Selected {len(selected_tests)} from {len(test_cases)} tests")
        return optimization_results
    
    async def load_test_cases(self, test_suite_path: str) -> List[TestCase]:
        """Load test cases with intelligent parsing"""
        test_cases = []
        
        if os.path.isdir(test_suite_path):
            for root, dirs, files in os.walk(test_suite_path):
                for file in files:
                    if self.is_test_file(file):
                        file_path = os.path.join(root, file)
                        test_case = await self.parse_test_file(file_path)
                        if test_case:
                            test_cases.append(test_case)
        
        return test_cases
    
    def is_test_file(self, filename: str) -> bool:
        """Determine if file is a test case"""
        test_extensions = ['.sv', '.v', '.vh', '.py', '.cpp', '.c', '.log', '.txt']
        test_patterns = ['test_', '_test', 'tb_', '_tb']
        
        return (any(filename.endswith(ext) for ext in test_extensions) and 
                any(pattern in filename.lower() for pattern in test_patterns))
    
    async def parse_test_file(self, file_path: str) -> Optional[TestCase]:
        """Parse test file using GenAI"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Use GenAI to parse and understand the test
            parsed_info = await self.llm_analyze_test_file(content, file_path)
            
            return TestCase(
                test_id=self.generate_test_id(file_path),
                test_name=os.path.basename(file_path),
                file_path=file_path,
                content=content,
                parameters=parsed_info.get('parameters', {}),
                execution_time=parsed_info.get('estimated_execution_time', 0.0),
                status=parsed_info.get('status', 'UNKNOWN')
            )
        
        except Exception as e:
            logger.warning(f"Failed to parse test file {file_path}: {e}")
            return None
    
    async def llm_analyze_test_file(self, content: str, file_path: str) -> Dict:
        """Use Gemini 2.5 Pro to analyze test file content"""
        # Optimized prompt for Gemini 2.5 Pro
        prompt = f"""Analyze this hardware verification test file:

File: {os.path.basename(file_path)}
Content:
{content[:1500]}

Provide analysis as JSON with these keys:
- test_type: one of [unit_test, integration_test, stress_test, corner_case, regression_test]
- complexity_level: one of [low, medium, high]
- parameters: object with test parameters
- estimated_execution_time: number (seconds)
- functional_areas: array of strings (design areas tested)
- bug_detection_potential: one of [low, medium, high]
- coverage_areas: array of strings (coverage areas)
- dependencies: array of strings (dependencies)
- uniqueness_indicators: array of strings (unique features)
- summary: string (brief description)

Return only the JSON object."""

        response = await self.call_llm(prompt)

        # Use improved JSON parsing
        fallback_data = get_test_analysis_fallback(file_path)
        parsed = clean_and_parse_json(response, fallback_data)

        if parsed == fallback_data:
            logger.warning(f"Using fallback analysis for {file_path}")

        return parsed
    
    async def call_llm(self, prompt: str) -> str:
        """Call the Gemini 2.5 Pro LLM with optimized settings"""
        try:
            # For Gemini 2.5 Pro, we can be more direct since it has better instruction following
            enhanced_prompt = f"""
{prompt}

Response format: Return only valid JSON. No explanations, no markdown formatting, just the JSON object.
"""

            # Use Gemini 2.5 Pro API - no need to pass safety settings again as they're in the model
            response = await asyncio.to_thread(
                self.llm_client.generate_content,
                enhanced_prompt
            )

            # Gemini 2.5 Pro should return cleaner responses with response_mime_type="application/json"
            if hasattr(response, 'text') and response.text:
                text = response.text.strip()

                # Gemini 2.5 Pro with JSON mime type should return clean JSON, but let's still validate
                if text and (text.startswith('{') or text.startswith('[')):
                    return text

                # Fallback cleaning for any edge cases
                if text.startswith('```json'):
                    text = text.replace('```json', '').replace('```', '').strip()
                elif text.startswith('```'):
                    text = text.replace('```', '').strip()

                # Try to extract JSON if it's embedded in text
                if not text.startswith('{') and not text.startswith('['):
                    import re
                    json_match = re.search(r'[\{\[].*[\}\]]', text, re.DOTALL)
                    if json_match:
                        text = json_match.group(0)
                    else:
                        logger.warning(f"No JSON found in Gemini 2.5 Pro response: {text[:200]}...")
                        return "{}"

                return text

            elif hasattr(response, 'candidates') and response.candidates:
                # Check finish reason
                candidate = response.candidates[0]
                if hasattr(candidate, 'finish_reason'):
                    if candidate.finish_reason == 1:  # STOP
                        logger.warning("Response finished normally but no text content")
                    elif candidate.finish_reason == 2:  # MAX_TOKENS
                        logger.warning("Response truncated due to max tokens limit")
                    elif candidate.finish_reason == 3:  # SAFETY
                        logger.warning("Response blocked by safety filters")
                    elif candidate.finish_reason == 4:  # RECITATION
                        logger.warning("Response blocked due to recitation")

                # Try to get partial content
                if hasattr(candidate, 'content') and candidate.content:
                    if hasattr(candidate.content, 'parts') and candidate.content.parts:
                        text = candidate.content.parts[0].text if candidate.content.parts[0].text else ""
                        if text:
                            return text

            logger.warning("Empty or invalid response from Gemini")
            return "{}"

        except Exception as e:
            logger.error(f"Gemini API call failed: {e}")
            return "{}"
    
    def generate_test_id(self, file_path: str) -> str:
        """Generate unique test ID"""
        return hashlib.md5(file_path.encode()).hexdigest()[:12]
    
    async def select_optimal_subset(self, prioritized_tests: List[TestCase], 
                                  coverage_gaps: Dict) -> List[TestCase]:
        """Select optimal test subset using GenAI reasoning"""
        
        # Create test summary for LLM analysis
        test_summaries = []
        for test in prioritized_tests:
            summary = {
                'id': test.test_id,
                'name': test.test_name,
                'priority_score': getattr(test, 'priority_score', 0.5),
                'bug_potential': getattr(test, 'bug_potential', 'medium'),
                'coverage_areas': getattr(test, 'coverage_areas', []),
                'execution_time': test.execution_time,
                'uniqueness': getattr(test, 'uniqueness_score', 0.5)
            }
            test_summaries.append(summary)
        
        selection_prompt = f"""
        You are an expert hardware verification engineer. Given {len(test_summaries)} test cases, 
        select the optimal subset of maximum {self.config['max_selected_tests']} tests.
        
        Test Cases Summary:
        {json.dumps(test_summaries[:50], indent=2)}  # Limit for token constraints
        
        Coverage Gaps Identified:
        {json.dumps(coverage_gaps, indent=2)}
        
        Selection Criteria (in order of importance):
        1. Maximize bug detection potential
        2. Ensure comprehensive coverage of all functional areas
        3. Include tests that cover identified coverage gaps
        4. Minimize execution time while maintaining quality
        5. Ensure diversity in test types and approaches
        6. Prioritize unique tests over similar ones
        
        Provide your selection as a JSON list of test IDs with reasoning:
        {{
            "selected_tests": ["test_id1", "test_id2", ...],
            "reasoning": {{
                "coverage_strategy": "explanation of coverage approach",
                "bug_detection_strategy": "explanation of bug detection approach",
                "optimization_rationale": "explanation of optimization decisions"
            }},
            "estimated_metrics": {{
                "coverage_percentage": 95.0,
                "time_savings": 0.75,
                "bug_detection_confidence": "high"
            }}
        }}
        """
        
        response = await self.call_llm(selection_prompt)
        try:
            selection_data = json.loads(response)
            selected_ids = selection_data.get('selected_tests', [])
            
            # Map IDs back to test cases
            id_to_test = {test.test_id: test for test in prioritized_tests}
            selected_tests = [id_to_test[test_id] for test_id in selected_ids if test_id in id_to_test]
            
            # Store selection reasoning
            for test in selected_tests:
                test.selection_reasoning = selection_data.get('reasoning', {})
            
            return selected_tests
        
        except json.JSONDecodeError:
            logger.error("Failed to parse test selection response")
            # Fallback: select top tests by priority
            return prioritized_tests[:self.config['max_selected_tests']]
    
    async def generate_optimization_report(self, original_tests: List[TestCase], 
                                         selected_tests: List[TestCase],
                                         redundancy_report: Dict, coverage_gaps: Dict,
                                         output_path: str) -> Dict[str, Any]:
        """Generate comprehensive optimization report using GenAI"""
        
        os.makedirs(output_path, exist_ok=True)
        
        # Generate detailed analysis report
        report_prompt = f"""
        Generate a comprehensive verification optimization report based on the following data:
        
        Original test count: {len(original_tests)}
        Selected test count: {len(selected_tests)}
        Reduction ratio: {len(selected_tests)/len(original_tests):.3f}
        
        Redundancy Analysis:
        {json.dumps(redundancy_report, indent=2)}
        
        Coverage Gaps:
        {json.dumps(coverage_gaps, indent=2)}
        
        Generate a professional report including:
        1. Executive Summary
        2. Optimization Methodology
        3. Redundancy Analysis Results
        4. Test Selection Strategy
        5. Coverage Analysis
        6. Risk Assessment
        7. Recommendations
        8. Expected Benefits
        
        Format as markdown for easy reading.
        """
        
        report_content = await self.call_llm(report_prompt)
        
        # Save main report
        with open(os.path.join(output_path, "optimization_report.md"), "w") as f:
            f.write(report_content)
        
        # Generate test list files
        self.generate_test_lists(selected_tests, output_path)
        
        # Generate EDA tool integration scripts
        await self.generate_eda_scripts(selected_tests, output_path)
        
        return {
            "original_count": len(original_tests),
            "selected_count": len(selected_tests),
            "reduction_ratio": len(selected_tests) / len(original_tests),
            "redundancy_removed": redundancy_report.get('total_redundant', 0),
            "coverage_gaps_addressed": len(coverage_gaps.get('gaps_covered', [])),
            "optimization_method": "GenAI-driven analysis",
            "report_path": output_path
        }
    
    def generate_test_lists(self, selected_tests: List[TestCase], output_path: str):
        """Generate various test list formats"""
        
        # Simple test list
        with open(os.path.join(output_path, "selected_tests.txt"), "w") as f:
            for test in selected_tests:
                f.write(f"{test.test_name}\n")
        
        # Detailed test list with metadata
        with open(os.path.join(output_path, "detailed_test_list.json"), "w") as f:
            test_data = []
            for test in selected_tests:
                test_info = {
                    "test_id": test.test_id,
                    "test_name": test.test_name,
                    "file_path": test.file_path,
                    "priority_score": getattr(test, 'priority_score', 0.5),
                    "estimated_time": test.execution_time,
                    "selection_reasoning": getattr(test, 'selection_reasoning', {})
                }
                test_data.append(test_info)
            json.dump(test_data, f, indent=2)
    
    async def generate_eda_scripts(self, selected_tests: List[TestCase], output_path: str):
        """Generate EDA tool integration scripts using GenAI"""
        
        script_generation_prompt = f"""
        Generate optimized simulation scripts for running {len(selected_tests)} selected test cases.
        
        Test Information:
        {json.dumps([{'name': t.test_name, 'path': t.file_path, 'time': t.execution_time} 
                    for t in selected_tests[:20]], indent=2)}
        
        Generate scripts for:
        1. VCS (Synopsys) - Makefile and bash script
        2. Questa (Mentor) - TCL script and batch file  
        3. Xcelium (Cadence) - TCL script and execution script
        
        Include:
        - Parallel execution optimization
        - Coverage collection
        - Result aggregation
        - Error handling
        - Progress reporting
        
        Provide each script separately with clear headers.
        """
        
        scripts_content = await self.call_llm(script_generation_prompt)
        
        # Save the generated scripts
        with open(os.path.join(output_path, "generated_eda_scripts.txt"), "w") as f:
            f.write(scripts_content)
        
        # Parse and save individual script files
        self.extract_and_save_scripts(scripts_content, output_path)
    
    def extract_and_save_scripts(self, scripts_content: str, output_path: str):
        """Extract individual scripts from GenAI response"""
        # Simple extraction based on common patterns
        script_patterns = {
            'vcs': r'# VCS Script.*?(?=# \w+ Script|\Z)',
            'questa': r'# Questa Script.*?(?=# \w+ Script|\Z)',
            'xcelium': r'# Xcelium Script.*?(?=# \w+ Script|\Z)'
        }
        
        for tool, pattern in script_patterns.items():
            match = re.search(pattern, scripts_content, re.DOTALL | re.IGNORECASE)
            if match:
                script_content = match.group(0)
                with open(os.path.join(output_path, f"{tool}_run_script.sh"), "w") as f:
                    f.write(script_content)
                    
                # Make executable
                os.chmod(os.path.join(output_path, f"{tool}_run_script.sh"), 0o755)


class GenAITestAnalyzer:
    """Analyze test cases using GenAI"""
    
    def __init__(self, llm_client, config):
        self.llm_client = llm_client
        self.config = config
    
    async def analyze_tests(self, test_cases: List[TestCase]) -> List[TestCase]:
        """Analyze test cases in batches"""
        analyzed_tests = []
        batch_size = self.config['batch_size']
        
        for i in range(0, len(test_cases), batch_size):
            batch = test_cases[i:i + batch_size]
            batch_results = await self.analyze_test_batch(batch)
            analyzed_tests.extend(batch_results)
        
        return analyzed_tests
    
    async def analyze_test_batch(self, test_batch: List[TestCase]) -> List[TestCase]:
        """Analyze a batch of tests using Gemini"""

        # Prepare batch analysis prompt
        test_summaries = []
        for test in test_batch:
            summary = {
                "test_id": test.test_id,
                "test_name": test.test_name,
                "file_path": test.file_path,
                "content_preview": test.content[:500] if test.content else "",
                "parameters": test.parameters
            }
            test_summaries.append(summary)

        batch_analysis_prompt = f"""
        Analyze the following batch of {len(test_batch)} hardware verification test cases.
        For each test, provide detailed analysis including:

        Test Cases:
        {json.dumps(test_summaries, indent=2)}

        For each test, analyze and provide:
        1. test_type: (unit_test, integration_test, regression_test, stress_test, corner_case, etc.)
        2. complexity_level: (low=1, medium=2, high=3) - numerical score
        3. bug_detection_potential: (low=1, medium=2, high=3) - numerical score
        4. coverage_areas: List of functional areas covered
        5. execution_time_estimate: Estimated runtime in seconds
        6. uniqueness_score: (0.0-1.0) - how unique this test is
        7. functional_areas: List of design modules/features tested
        8. test_patterns: List of test patterns used (directed, random, constrained_random, etc.)
        9. verification_intent: Primary purpose of this test
        10. risk_level: (low=1, medium=2, high=3) - risk if this test is skipped

        Respond with a JSON object where each key is the test_id and value contains the analysis:
        {{
            "test_id_1": {{
                "test_type": "unit_test",
                "complexity_level": 2,
                "bug_detection_potential": 3,
                "coverage_areas": ["alu", "register_file"],
                "execution_time_estimate": 45.0,
                "uniqueness_score": 0.8,
                "functional_areas": ["arithmetic_operations", "data_path"],
                "test_patterns": ["directed", "corner_case"],
                "verification_intent": "Verify ALU corner cases",
                "risk_level": 3
            }},
            ...
        }}
        """

        try:
            # Call Gemini for batch analysis
            response = await self.call_llm_async(batch_analysis_prompt)
            analysis_results = json.loads(response)

            # Apply analysis results to test cases
            for test in test_batch:
                if test.test_id in analysis_results:
                    analysis = analysis_results[test.test_id]

                    # Add analysis attributes to test case
                    test.test_type = analysis.get('test_type', 'unknown')
                    test.complexity_level = analysis.get('complexity_level', 2)
                    test.bug_detection_potential = analysis.get('bug_detection_potential', 2)
                    test.coverage_areas = analysis.get('coverage_areas', [])
                    test.execution_time = analysis.get('execution_time_estimate', test.execution_time)
                    test.uniqueness_score = analysis.get('uniqueness_score', 0.5)
                    test.functional_areas = analysis.get('functional_areas', [])
                    test.test_patterns = analysis.get('test_patterns', [])
                    test.verification_intent = analysis.get('verification_intent', '')
                    test.risk_level = analysis.get('risk_level', 2)

                    # Calculate composite priority score
                    test.priority_score = self.calculate_priority_score(test)

            return test_batch

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse batch analysis response: {e}")
            # Fallback: assign default values
            for test in test_batch:
                test.test_type = 'unknown'
                test.complexity_level = 2
                test.bug_detection_potential = 2
                test.coverage_areas = []
                test.uniqueness_score = 0.5
                test.functional_areas = []
                test.test_patterns = []
                test.verification_intent = ''
                test.risk_level = 2
                test.priority_score = 0.5

            return test_batch

        except Exception as e:
            logger.error(f"Batch analysis failed: {e}")
            return test_batch

    async def call_llm_async(self, prompt: str) -> str:
        """Async wrapper for LLM calls"""
        try:
            response = await asyncio.to_thread(
                self.llm_client.generate_content,
                prompt
            )
            return response.text if response.text else "{}"
        except Exception as e:
            logger.error(f"LLM call failed: {e}")
            return "{}"

    def calculate_priority_score(self, test: TestCase) -> float:
        """Calculate composite priority score for a test"""
        # Weighted combination of different factors
        weights = {
            'bug_detection': 0.4,
            'uniqueness': 0.3,
            'risk': 0.2,
            'complexity': 0.1
        }

        # Normalize scores to 0-1 range
        bug_score = (test.bug_detection_potential - 1) / 2  # 1-3 -> 0-1
        uniqueness_score = test.uniqueness_score  # Already 0-1
        risk_score = (test.risk_level - 1) / 2  # 1-3 -> 0-1
        complexity_score = (test.complexity_level - 1) / 2  # 1-3 -> 0-1

        priority = (
            weights['bug_detection'] * bug_score +
            weights['uniqueness'] * uniqueness_score +
            weights['risk'] * risk_score +
            weights['complexity'] * complexity_score
        )

        return min(1.0, max(0.0, priority))  # Clamp to 0-1 range


class GenAIRedundancyDetector:
    """Detect redundant tests using GenAI reasoning"""
    
    def __init__(self, llm_client, config):
        self.llm_client = llm_client
        self.config = config
    
    async def detect_redundant_tests(self, test_cases: List[TestCase]) -> Tuple[List[TestCase], Dict]:
        """Detect redundant tests using GenAI semantic analysis"""

        logger.info(f"Starting redundancy detection for {len(test_cases)} tests")

        # Group tests by similarity for efficient analysis
        similarity_groups = await self.group_tests_by_similarity(test_cases)

        redundant_tests = set()
        redundancy_groups = []

        # Analyze each similarity group for redundancy
        for group_id, group_tests in similarity_groups.items():
            if len(group_tests) > 1:
                group_redundancies = await self.analyze_group_redundancy(group_tests)
                if group_redundancies:
                    redundancy_groups.append({
                        'group_id': group_id,
                        'tests_in_group': len(group_tests),
                        'redundant_tests': group_redundancies['redundant_test_ids'],
                        'kept_test': group_redundancies['representative_test'],
                        'redundancy_reason': group_redundancies['reason']
                    })
                    redundant_tests.update(group_redundancies['redundant_test_ids'])

        # Filter out redundant tests
        unique_tests = [test for test in test_cases if test.test_id not in redundant_tests]

        redundancy_report = {
            "total_redundant": len(redundant_tests),
            "redundancy_groups": redundancy_groups,
            "original_count": len(test_cases),
            "unique_count": len(unique_tests),
            "reduction_percentage": (len(redundant_tests) / len(test_cases)) * 100 if test_cases else 0
        }

        logger.info(f"Redundancy detection completed. Removed {len(redundant_tests)} redundant tests")
        return unique_tests, redundancy_report

    async def group_tests_by_similarity(self, test_cases: List[TestCase]) -> Dict[str, List[TestCase]]:
        """Group tests by similarity using GenAI analysis"""

        # Create test summaries for similarity analysis
        test_summaries = []
        for test in test_cases:
            summary = {
                'test_id': test.test_id,
                'test_name': test.test_name,
                'test_type': getattr(test, 'test_type', 'unknown'),
                'functional_areas': getattr(test, 'functional_areas', []),
                'coverage_areas': getattr(test, 'coverage_areas', []),
                'verification_intent': getattr(test, 'verification_intent', ''),
                'test_patterns': getattr(test, 'test_patterns', []),
                'content_hash': hashlib.md5(test.content.encode()).hexdigest()[:8] if test.content else ''
            }
            test_summaries.append(summary)

        # Use GenAI to identify similarity groups
        grouping_prompt = f"""
        Analyze the following {len(test_summaries)} hardware verification tests and group them by similarity.
        Tests that verify the same functionality, use similar approaches, or have overlapping coverage should be grouped together.

        Test Summaries:
        {json.dumps(test_summaries[:50], indent=2)}  # Limit for token constraints

        Group tests based on:
        1. Functional similarity (testing same features)
        2. Coverage overlap (testing same code areas)
        3. Test pattern similarity (same testing approach)
        4. Verification intent similarity (same purpose)

        Respond with JSON format:
        {{
            "similarity_groups": {{
                "group_1": {{
                    "test_ids": ["test_id1", "test_id2", ...],
                    "similarity_reason": "Both test ALU arithmetic operations",
                    "similarity_score": 0.85
                }},
                "group_2": {{
                    "test_ids": ["test_id3", "test_id4", ...],
                    "similarity_reason": "Both test memory interface",
                    "similarity_score": 0.90
                }},
                ...
            }}
        }}

        Only group tests with similarity_score >= {self.config['similarity_threshold']}.
        """

        try:
            response = await self.call_llm_async(grouping_prompt)
            grouping_results = json.loads(response)

            # Convert to our format
            similarity_groups = {}
            test_id_to_test = {test.test_id: test for test in test_cases}

            for group_id, group_data in grouping_results.get('similarity_groups', {}).items():
                test_ids = group_data.get('test_ids', [])
                group_tests = [test_id_to_test[tid] for tid in test_ids if tid in test_id_to_test]

                if len(group_tests) > 1:
                    similarity_groups[group_id] = group_tests

            # Add ungrouped tests as individual groups
            grouped_test_ids = set()
            for group_tests in similarity_groups.values():
                grouped_test_ids.update(test.test_id for test in group_tests)

            for test in test_cases:
                if test.test_id not in grouped_test_ids:
                    similarity_groups[f"single_{test.test_id}"] = [test]

            return similarity_groups

        except Exception as e:
            logger.error(f"Similarity grouping failed: {e}")
            # Fallback: each test in its own group
            return {f"single_{test.test_id}": [test] for test in test_cases}

    async def analyze_group_redundancy(self, group_tests: List[TestCase]) -> Optional[Dict]:
        """Analyze a group of similar tests for redundancy"""

        if len(group_tests) <= 1:
            return None

        # Prepare detailed comparison
        test_details = []
        for test in group_tests:
            details = {
                'test_id': test.test_id,
                'test_name': test.test_name,
                'priority_score': getattr(test, 'priority_score', 0.5),
                'bug_detection_potential': getattr(test, 'bug_detection_potential', 2),
                'uniqueness_score': getattr(test, 'uniqueness_score', 0.5),
                'coverage_areas': getattr(test, 'coverage_areas', []),
                'functional_areas': getattr(test, 'functional_areas', []),
                'execution_time': test.execution_time,
                'verification_intent': getattr(test, 'verification_intent', ''),
                'content_preview': test.content[:300] if test.content else ''
            }
            test_details.append(details)

        redundancy_prompt = f"""
        Analyze this group of {len(group_tests)} similar hardware verification tests for redundancy.
        Determine which tests are truly redundant and which should be kept.

        Test Details:
        {json.dumps(test_details, indent=2)}

        Analysis criteria:
        1. Tests are redundant if they verify exactly the same functionality with the same approach
        2. Tests with higher bug_detection_potential should be preferred
        3. Tests with higher uniqueness_score should be preferred
        4. Tests with broader coverage_areas should be preferred
        5. Consider execution_time as a tiebreaker (prefer faster tests if equal value)

        Respond with JSON:
        {{
            "has_redundancy": true/false,
            "representative_test": "test_id_to_keep",
            "redundant_test_ids": ["test_id1", "test_id2", ...],
            "reason": "Detailed explanation of why these tests are redundant",
            "confidence": 0.85
        }}

        Only mark tests as redundant if confidence >= 0.8.
        """

        try:
            response = await self.call_llm_async(redundancy_prompt)
            redundancy_analysis = json.loads(response)

            if (redundancy_analysis.get('has_redundancy', False) and
                redundancy_analysis.get('confidence', 0) >= 0.8):
                return redundancy_analysis

            return None

        except Exception as e:
            logger.error(f"Redundancy analysis failed for group: {e}")
            return None

    async def call_llm_async(self, prompt: str) -> str:
        """Async wrapper for LLM calls"""
        try:
            response = await asyncio.to_thread(
                self.llm_client.generate_content,
                prompt
            )
            return response.text if response.text else "{}"
        except Exception as e:
            logger.error(f"LLM call failed: {e}")
            return "{}"


class GenAITestPrioritizer:
    """Prioritize tests using GenAI reasoning"""
    
    def __init__(self, llm_client, config):
        self.llm_client = llm_client
        self.config = config
    
    async def prioritize_tests(self, test_cases: List[TestCase]) -> List[TestCase]:
        """Prioritize tests using GenAI analysis"""

        logger.info(f"Starting test prioritization for {len(test_cases)} tests")

        # Analyze tests in batches for prioritization
        batch_size = self.config['batch_size']
        prioritized_tests = []

        for i in range(0, len(test_cases), batch_size):
            batch = test_cases[i:i + batch_size]
            batch_prioritized = await self.prioritize_test_batch(batch)
            prioritized_tests.extend(batch_prioritized)

        # Perform global ranking adjustment
        globally_ranked_tests = await self.perform_global_ranking(prioritized_tests)

        # Sort by final priority score (descending)
        globally_ranked_tests.sort(
            key=lambda t: getattr(t, 'final_priority_score', 0.5),
            reverse=True
        )

        logger.info("Test prioritization completed")
        return globally_ranked_tests

    async def prioritize_test_batch(self, test_batch: List[TestCase]) -> List[TestCase]:
        """Prioritize a batch of tests using GenAI"""

        # Prepare test data for prioritization analysis
        test_data = []
        for test in test_batch:
            data = {
                'test_id': test.test_id,
                'test_name': test.test_name,
                'test_type': getattr(test, 'test_type', 'unknown'),
                'complexity_level': getattr(test, 'complexity_level', 2),
                'bug_detection_potential': getattr(test, 'bug_detection_potential', 2),
                'coverage_areas': getattr(test, 'coverage_areas', []),
                'functional_areas': getattr(test, 'functional_areas', []),
                'uniqueness_score': getattr(test, 'uniqueness_score', 0.5),
                'execution_time': test.execution_time,
                'risk_level': getattr(test, 'risk_level', 2),
                'verification_intent': getattr(test, 'verification_intent', ''),
                'test_patterns': getattr(test, 'test_patterns', [])
            }
            test_data.append(data)

        prioritization_prompt = f"""
        You are an expert hardware verification engineer. Prioritize the following {len(test_batch)} test cases
        based on their value for finding bugs and ensuring comprehensive verification coverage.

        Test Cases:
        {json.dumps(test_data, indent=2)}

        Prioritization Criteria (in order of importance):
        1. Bug Detection Potential (40%): Tests more likely to find bugs should be prioritized
        2. Coverage Value (25%): Tests covering critical or under-tested areas
        3. Risk Mitigation (20%): Tests that mitigate high-risk scenarios
        4. Uniqueness (10%): Tests that provide unique verification value
        5. Efficiency (5%): Consider execution time vs. value ratio

        For each test, calculate a priority score (0.0-1.0) and provide reasoning.

        Respond with JSON:
        {{
            "prioritized_tests": {{
                "test_id_1": {{
                    "priority_score": 0.95,
                    "bug_detection_score": 0.9,
                    "coverage_value_score": 0.8,
                    "risk_mitigation_score": 1.0,
                    "uniqueness_value": 0.7,
                    "efficiency_score": 0.6,
                    "priority_reasoning": "High-value corner case test for critical ALU operations",
                    "recommended_execution_order": 1
                }},
                ...
            }},
            "batch_summary": {{
                "high_priority_count": 3,
                "medium_priority_count": 5,
                "low_priority_count": 2,
                "total_estimated_time": 450.0
            }}
        }}
        """

        try:
            response = await self.call_llm_async(prioritization_prompt)
            prioritization_results = json.loads(response)

            # Apply prioritization results to test cases
            for test in test_batch:
                if test.test_id in prioritization_results.get('prioritized_tests', {}):
                    priority_data = prioritization_results['prioritized_tests'][test.test_id]

                    test.priority_score = priority_data.get('priority_score', 0.5)
                    test.bug_detection_score = priority_data.get('bug_detection_score', 0.5)
                    test.coverage_value_score = priority_data.get('coverage_value_score', 0.5)
                    test.risk_mitigation_score = priority_data.get('risk_mitigation_score', 0.5)
                    test.uniqueness_value = priority_data.get('uniqueness_value', 0.5)
                    test.efficiency_score = priority_data.get('efficiency_score', 0.5)
                    test.priority_reasoning = priority_data.get('priority_reasoning', '')
                    test.recommended_execution_order = priority_data.get('recommended_execution_order', 999)
                else:
                    # Fallback scoring
                    test.priority_score = getattr(test, 'priority_score', 0.5)
                    test.bug_detection_score = 0.5
                    test.coverage_value_score = 0.5
                    test.risk_mitigation_score = 0.5
                    test.uniqueness_value = getattr(test, 'uniqueness_score', 0.5)
                    test.efficiency_score = 0.5
                    test.priority_reasoning = 'Default prioritization applied'
                    test.recommended_execution_order = 999

            return test_batch

        except Exception as e:
            logger.error(f"Batch prioritization failed: {e}")
            # Fallback: use existing priority scores or defaults
            for test in test_batch:
                if not hasattr(test, 'priority_score'):
                    test.priority_score = 0.5
                test.bug_detection_score = 0.5
                test.coverage_value_score = 0.5
                test.risk_mitigation_score = 0.5
                test.uniqueness_value = getattr(test, 'uniqueness_score', 0.5)
                test.efficiency_score = 0.5
                test.priority_reasoning = 'Fallback prioritization'
                test.recommended_execution_order = 999

            return test_batch

    async def perform_global_ranking(self, test_cases: List[TestCase]) -> List[TestCase]:
        """Perform global ranking adjustment across all tests"""

        # Collect global statistics
        all_scores = {
            'priority': [getattr(t, 'priority_score', 0.5) for t in test_cases],
            'bug_detection': [getattr(t, 'bug_detection_score', 0.5) for t in test_cases],
            'coverage': [getattr(t, 'coverage_value_score', 0.5) for t in test_cases],
            'risk': [getattr(t, 'risk_mitigation_score', 0.5) for t in test_cases],
            'uniqueness': [getattr(t, 'uniqueness_value', 0.5) for t in test_cases]
        }

        # Calculate global statistics for normalization
        global_stats = {}
        for score_type, scores in all_scores.items():
            global_stats[score_type] = {
                'mean': sum(scores) / len(scores) if scores else 0.5,
                'max': max(scores) if scores else 1.0,
                'min': min(scores) if scores else 0.0
            }

        # Create global ranking prompt
        test_summaries = []
        for test in test_cases[:100]:  # Limit for token constraints
            summary = {
                'test_id': test.test_id,
                'test_name': test.test_name,
                'priority_score': getattr(test, 'priority_score', 0.5),
                'bug_detection_score': getattr(test, 'bug_detection_score', 0.5),
                'coverage_value_score': getattr(test, 'coverage_value_score', 0.5),
                'functional_areas': getattr(test, 'functional_areas', []),
                'test_type': getattr(test, 'test_type', 'unknown')
            }
            test_summaries.append(summary)

        global_ranking_prompt = f"""
        Perform global ranking adjustment for hardware verification test suite.

        Global Statistics:
        {json.dumps(global_stats, indent=2)}

        Top Test Candidates (sample):
        {json.dumps(test_summaries, indent=2)}

        Adjust rankings considering:
        1. Global distribution of scores
        2. Ensure diverse coverage across functional areas
        3. Balance between different test types
        4. Avoid over-concentration in single areas

        Provide adjustment factors for different categories:
        {{
            "adjustment_factors": {{
                "high_priority_boost": 1.1,
                "coverage_diversity_boost": 1.05,
                "test_type_balance_factor": 1.02,
                "functional_area_spread_factor": 1.03
            }},
            "global_insights": {{
                "coverage_gaps": ["area1", "area2"],
                "over_represented_areas": ["area3"],
                "recommended_focus": "corner_cases"
            }}
        }}
        """

        try:
            response = await self.call_llm_async(global_ranking_prompt)
            global_analysis = json.loads(response)

            adjustment_factors = global_analysis.get('adjustment_factors', {})

            # Apply global adjustments
            for test in test_cases:
                base_score = getattr(test, 'priority_score', 0.5)

                # Apply various adjustment factors
                adjusted_score = base_score

                # High priority boost
                if base_score > global_stats['priority']['mean'] * 1.2:
                    adjusted_score *= adjustment_factors.get('high_priority_boost', 1.0)

                # Coverage diversity boost
                functional_areas = getattr(test, 'functional_areas', [])
                if len(functional_areas) > 2:
                    adjusted_score *= adjustment_factors.get('coverage_diversity_boost', 1.0)

                # Test type balance
                test_type = getattr(test, 'test_type', 'unknown')
                if test_type in ['corner_case', 'stress_test']:
                    adjusted_score *= adjustment_factors.get('test_type_balance_factor', 1.0)

                test.final_priority_score = min(1.0, adjusted_score)

            return test_cases

        except Exception as e:
            logger.error(f"Global ranking failed: {e}")
            # Fallback: use original priority scores
            for test in test_cases:
                test.final_priority_score = getattr(test, 'priority_score', 0.5)

            return test_cases

    async def call_llm_async(self, prompt: str) -> str:
        """Async wrapper for LLM calls"""
        try:
            response = await asyncio.to_thread(
                self.llm_client.generate_content,
                prompt
            )
            return response.text if response.text else "{}"
        except Exception as e:
            logger.error(f"LLM call failed: {e}")
            return "{}"


class GenAICoverageAnalyzer:
    """Analyze coverage using GenAI"""
    
    def __init__(self, llm_client, config):
        self.llm_client = llm_client
        self.config = config
    
    async def analyze_coverage_gaps(self, test_cases: List[TestCase]) -> Dict:
        """Analyze coverage gaps using GenAI reasoning"""

        logger.info(f"Starting coverage gap analysis for {len(test_cases)} tests")

        # Collect coverage information from all tests
        coverage_data = self.collect_coverage_data(test_cases)

        # Analyze coverage gaps using GenAI
        gap_analysis = await self.identify_coverage_gaps(coverage_data, test_cases)

        # Suggest tests to fill gaps
        gap_filling_suggestions = await self.suggest_gap_filling_tests(gap_analysis, test_cases)

        # Validate coverage completeness
        coverage_completeness = await self.assess_coverage_completeness(coverage_data, gap_analysis)

        result = {
            "gaps_identified": gap_analysis.get('gaps', []),
            "gaps_covered": gap_analysis.get('covered_areas', []),
            "coverage_completeness": coverage_completeness,
            "gap_filling_suggestions": gap_filling_suggestions,
            "coverage_metrics": coverage_data['metrics'],
            "critical_gaps": gap_analysis.get('critical_gaps', []),
            "recommendations": gap_analysis.get('recommendations', [])
        }

        logger.info(f"Coverage analysis completed. Found {len(result['gaps_identified'])} gaps")
        return result

    def collect_coverage_data(self, test_cases: List[TestCase]) -> Dict:
        """Collect and organize coverage data from test cases"""

        coverage_data = {
            'functional_areas': defaultdict(list),
            'coverage_areas': defaultdict(list),
            'test_types': defaultdict(list),
            'verification_intents': defaultdict(list),
            'test_patterns': defaultdict(list),
            'metrics': {}
        }

        # Aggregate coverage information
        all_functional_areas = set()
        all_coverage_areas = set()
        all_test_types = set()
        all_verification_intents = set()
        all_test_patterns = set()

        for test in test_cases:
            # Functional areas
            func_areas = getattr(test, 'functional_areas', [])
            for area in func_areas:
                coverage_data['functional_areas'][area].append(test.test_id)
                all_functional_areas.add(area)

            # Coverage areas
            cov_areas = getattr(test, 'coverage_areas', [])
            for area in cov_areas:
                coverage_data['coverage_areas'][area].append(test.test_id)
                all_coverage_areas.add(area)

            # Test types
            test_type = getattr(test, 'test_type', 'unknown')
            coverage_data['test_types'][test_type].append(test.test_id)
            all_test_types.add(test_type)

            # Verification intents
            intent = getattr(test, 'verification_intent', '')
            if intent:
                coverage_data['verification_intents'][intent].append(test.test_id)
                all_verification_intents.add(intent)

            # Test patterns
            patterns = getattr(test, 'test_patterns', [])
            for pattern in patterns:
                coverage_data['test_patterns'][pattern].append(test.test_id)
                all_test_patterns.add(pattern)

        # Calculate metrics
        coverage_data['metrics'] = {
            'total_tests': len(test_cases),
            'functional_areas_covered': len(all_functional_areas),
            'coverage_areas_covered': len(all_coverage_areas),
            'test_types_used': len(all_test_types),
            'unique_verification_intents': len(all_verification_intents),
            'test_patterns_used': len(all_test_patterns),
            'average_tests_per_functional_area': len(test_cases) / len(all_functional_areas) if all_functional_areas else 0,
            'coverage_distribution': dict(coverage_data['functional_areas'])
        }

        return coverage_data

    async def identify_coverage_gaps(self, coverage_data: Dict, test_cases: List[TestCase]) -> Dict:
        """Identify coverage gaps using GenAI analysis"""

        gap_analysis_prompt = f"""
        Analyze the following hardware verification test suite coverage data to identify gaps and weaknesses.

        Coverage Summary:
        - Total tests: {coverage_data['metrics']['total_tests']}
        - Functional areas covered: {coverage_data['metrics']['functional_areas_covered']}
        - Coverage areas covered: {coverage_data['metrics']['coverage_areas_covered']}
        - Test types used: {coverage_data['metrics']['test_types_used']}

        Functional Area Coverage:
        {json.dumps(dict(coverage_data['functional_areas']), indent=2)}

        Coverage Area Distribution:
        {json.dumps(dict(coverage_data['coverage_areas']), indent=2)}

        Test Type Distribution:
        {json.dumps(dict(coverage_data['test_types']), indent=2)}

        Test Pattern Usage:
        {json.dumps(dict(coverage_data['test_patterns']), indent=2)}

        Identify:
        1. Functional areas with insufficient test coverage
        2. Missing test types (unit, integration, stress, corner case, etc.)
        3. Under-represented verification patterns
        4. Critical scenarios that may be missing
        5. Areas with only single test coverage (risk points)

        For hardware verification, consider typical areas that need coverage:
        - Data path operations
        - Control logic
        - Interface protocols
        - Error handling
        - Corner cases and boundary conditions
        - Performance scenarios
        - Power management
        - Reset and initialization
        - Interrupt handling
        - Memory operations

        Respond with JSON:
        {{
            "gaps": [
                {{
                    "gap_type": "functional_area",
                    "area": "interrupt_handling",
                    "severity": "high",
                    "description": "No tests found for interrupt handling mechanisms",
                    "impact": "Critical functionality untested",
                    "suggested_test_count": 3
                }},
                ...
            ],
            "covered_areas": [
                {{
                    "area": "alu_operations",
                    "coverage_level": "good",
                    "test_count": 15,
                    "coverage_quality": "comprehensive"
                }},
                ...
            ],
            "critical_gaps": [
                {{
                    "gap": "error_recovery",
                    "criticality": "high",
                    "business_impact": "System reliability risk"
                }}
            ],
            "recommendations": [
                "Add stress tests for memory interface",
                "Include corner case tests for boundary conditions",
                "Add negative testing for error scenarios"
            ]
        }}
        """

        try:
            response = await self.call_llm_async(gap_analysis_prompt)
            gap_analysis = json.loads(response)
            return gap_analysis

        except Exception as e:
            logger.error(f"Coverage gap analysis failed: {e}")
            return {
                "gaps": [],
                "covered_areas": [],
                "critical_gaps": [],
                "recommendations": []
            }

    async def suggest_gap_filling_tests(self, gap_analysis: Dict, test_cases: List[TestCase]) -> List[Dict]:
        """Suggest specific tests to fill identified coverage gaps"""

        gaps = gap_analysis.get('gaps', [])
        if not gaps:
            return []

        suggestion_prompt = f"""
        Based on the identified coverage gaps, suggest specific test cases to fill these gaps.

        Identified Gaps:
        {json.dumps(gaps, indent=2)}

        Existing Test Types:
        {json.dumps(list(set(getattr(t, 'test_type', 'unknown') for t in test_cases)), indent=2)}

        For each gap, suggest:
        1. Specific test case names
        2. Test approach and methodology
        3. Expected coverage improvement
        4. Implementation priority
        5. Estimated development effort

        Focus on practical, implementable test suggestions that would effectively fill the gaps.

        Respond with JSON:
        {{
            "gap_filling_tests": [
                {{
                    "gap_addressed": "interrupt_handling",
                    "suggested_tests": [
                        {{
                            "test_name": "test_interrupt_priority_handling",
                            "test_type": "functional_test",
                            "description": "Verify interrupt priority mechanisms work correctly",
                            "approach": "directed_test",
                            "priority": "high",
                            "estimated_effort": "medium",
                            "coverage_improvement": "Fills critical interrupt handling gap"
                        }},
                        ...
                    ]
                }},
                ...
            ],
            "implementation_order": [
                "test_interrupt_priority_handling",
                "test_error_recovery_mechanisms",
                ...
            ]
        }}
        """

        try:
            response = await self.call_llm_async(suggestion_prompt)
            suggestions = json.loads(response)
            return suggestions.get('gap_filling_tests', [])

        except Exception as e:
            logger.error(f"Gap filling suggestion failed: {e}")
            return []

    async def assess_coverage_completeness(self, coverage_data: Dict, gap_analysis: Dict) -> Dict:
        """Assess overall coverage completeness"""

        completeness_prompt = f"""
        Assess the overall completeness of this hardware verification test suite.

        Coverage Metrics:
        {json.dumps(coverage_data['metrics'], indent=2)}

        Identified Gaps:
        {len(gap_analysis.get('gaps', []))} gaps found

        Critical Gaps:
        {len(gap_analysis.get('critical_gaps', []))} critical gaps

        Provide an overall assessment:
        {{
            "completeness_score": 0.75,
            "completeness_level": "good",
            "strengths": [
                "Comprehensive ALU testing",
                "Good coverage of data path operations"
            ],
            "weaknesses": [
                "Limited error handling tests",
                "Missing stress test scenarios"
            ],
            "overall_assessment": "Test suite provides good basic coverage but needs enhancement in error scenarios",
            "confidence_level": "high",
            "recommended_actions": [
                "Add error handling tests",
                "Include more corner case scenarios"
            ]
        }}
        """

        try:
            response = await self.call_llm_async(completeness_prompt)
            completeness = json.loads(response)
            return completeness

        except Exception as e:
            logger.error(f"Coverage completeness assessment failed: {e}")
            return {
                "completeness_score": 0.5,
                "completeness_level": "unknown",
                "strengths": [],
                "weaknesses": [],
                "overall_assessment": "Assessment failed",
                "confidence_level": "low",
                "recommended_actions": []
            }

    async def call_llm_async(self, prompt: str) -> str:
        """Async wrapper for LLM calls"""
        try:
            response = await asyncio.to_thread(
                self.llm_client.generate_content,
                prompt
            )
            return response.text if response.text else "{}"
        except Exception as e:
            logger.error(f"LLM call failed: {e}")
            return "{}"


# Main execution
async def main():
    """Main execution function"""
    optimizer = GenAIVerificationOptimizer()
    
    # Example usage
    test_suite_path = "./test_suite/"
    output_path = "./optimized_results/"
    
    results = await optimizer.optimize_test_suite(test_suite_path, output_path)
    
    print(f"Optimization Results:")
    print(f"Original tests: {results['original_count']}")
    print(f"Selected tests: {results['selected_count']}")
    print(f"Reduction ratio: {results['reduction_ratio']:.3f}")
    print(f"Reports saved to: {results['report_path']}")


if __name__ == "__main__":
    asyncio.run(main())