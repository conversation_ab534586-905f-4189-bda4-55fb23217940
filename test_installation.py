#!/usr/bin/env python3
"""
Installation Test Script for GenAI Verification Test Optimization
Tests all major components and dependencies
"""

import os
import sys
import asyncio
import json
from pathlib import Path

def test_imports():
    """Test all required imports"""
    print("Testing imports...")
    
    try:
        import google.generativeai as genai
        print("✓ Google Generative AI imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import google.generativeai: {e}")
        return False
    
    try:
        from btp import GenAIVerificationOptimizer
        print("✓ GenAI Verification Optimizer imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import GenAI Verification Optimizer: {e}")
        return False
    
    try:
        import pandas as pd
        import numpy as np
        print("✓ Data processing libraries imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import data processing libraries: {e}")
        return False
    
    return True

def test_api_key():
    """Test API key configuration"""
    print("\nTesting API key configuration...")
    
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("✗ GEMINI_API_KEY environment variable not set")
        print("  Please set your API key: export GEMINI_API_KEY=your_key_here")
        return False
    
    if len(api_key) < 20:
        print("✗ API key seems too short, please verify")
        return False
    
    print("✓ API key found and appears valid")
    return True

async def test_gemini_connection():
    """Test connection to Gemini API"""
    print("\nTesting Gemini API connection...")
    
    try:
        import google.generativeai as genai
        
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            print("✗ No API key available for testing")
            return False
        
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-2.5-pro')
        
        response = await asyncio.to_thread(
            model.generate_content, 
            "Hello! Please respond with 'API connection successful' to confirm the connection."
        )
        
        if response.text and "successful" in response.text.lower():
            print("✓ Gemini API connection successful")
            return True
        else:
            print(f"✗ Unexpected response from Gemini: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Failed to connect to Gemini API: {e}")
        return False

def test_configuration():
    """Test configuration file"""
    print("\nTesting configuration...")
    
    config_path = "genai_config.json"
    if not os.path.exists(config_path):
        print(f"✗ Configuration file {config_path} not found")
        return False
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        required_keys = ['llm_provider', 'model_name', 'max_tokens', 'batch_size']
        for key in required_keys:
            if key not in config:
                print(f"✗ Missing required configuration key: {key}")
                return False
        
        print("✓ Configuration file is valid")
        return True
        
    except json.JSONDecodeError as e:
        print(f"✗ Invalid JSON in configuration file: {e}")
        return False
    except Exception as e:
        print(f"✗ Error reading configuration: {e}")
        return False

def test_test_suite():
    """Test test suite directory and files"""
    print("\nTesting test suite...")
    
    test_suite_path = "test_suite"
    if not os.path.exists(test_suite_path):
        print(f"✗ Test suite directory {test_suite_path} not found")
        return False
    
    test_files = []
    for root, dirs, files in os.walk(test_suite_path):
        for file in files:
            if file.endswith(('.sv', '.v', '.vh')):
                test_files.append(os.path.join(root, file))
    
    if not test_files:
        print("✗ No test files found in test suite")
        return False
    
    print(f"✓ Found {len(test_files)} test files")
    for file in test_files[:5]:  # Show first 5 files
        print(f"  - {file}")
    if len(test_files) > 5:
        print(f"  ... and {len(test_files) - 5} more")
    
    return True

async def test_optimizer_initialization():
    """Test optimizer initialization"""
    print("\nTesting optimizer initialization...")
    
    try:
        from btp import GenAIVerificationOptimizer
        
        optimizer = GenAIVerificationOptimizer()
        print("✓ Optimizer initialized successfully")
        
        # Test configuration loading
        if hasattr(optimizer, 'config') and optimizer.config:
            print("✓ Configuration loaded successfully")
        else:
            print("✗ Configuration not loaded properly")
            return False
        
        # Test LLM client initialization
        if hasattr(optimizer, 'llm_client') and optimizer.llm_client:
            print("✓ LLM client initialized successfully")
        else:
            print("✗ LLM client not initialized properly")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Failed to initialize optimizer: {e}")
        return False

async def test_file_parsing():
    """Test file parsing functionality"""
    print("\nTesting file parsing...")
    
    try:
        from btp import GenAIVerificationOptimizer
        
        optimizer = GenAIVerificationOptimizer()
        
        # Test with sample test file
        test_files = []
        for root, dirs, files in os.walk("test_suite"):
            for file in files:
                if file.endswith(('.sv', '.v')):
                    test_files.append(os.path.join(root, file))
                    break
            if test_files:
                break
        
        if not test_files:
            print("✗ No test files available for parsing test")
            return False
        
        test_case = await optimizer.parse_test_file(test_files[0])
        if test_case:
            print(f"✓ Successfully parsed test file: {test_case.test_name}")
            return True
        else:
            print("✗ Failed to parse test file")
            return False
            
    except Exception as e:
        print(f"✗ Error during file parsing test: {e}")
        return False

def test_output_directory():
    """Test output directory creation"""
    print("\nTesting output directory...")
    
    output_path = "test_output"
    try:
        os.makedirs(output_path, exist_ok=True)
        
        if os.path.exists(output_path) and os.path.isdir(output_path):
            print(f"✓ Output directory {output_path} created successfully")
            return True
        else:
            print(f"✗ Failed to create output directory {output_path}")
            return False
            
    except Exception as e:
        print(f"✗ Error creating output directory: {e}")
        return False

async def run_all_tests():
    """Run all tests"""
    print("=" * 60)
    print("GenAI Verification Test Optimization - Installation Test")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("API Key Test", test_api_key),
        ("Gemini Connection Test", test_gemini_connection),
        ("Configuration Test", test_configuration),
        ("Test Suite Test", test_test_suite),
        ("Optimizer Initialization Test", test_optimizer_initialization),
        ("File Parsing Test", test_file_parsing),
        ("Output Directory Test", test_output_directory),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"Running: {test_name}")
        print(f"{'-' * 40}")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'=' * 60}")
    print("TEST SUMMARY")
    print(f"{'=' * 60}")
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        symbol = "✓" if result else "✗"
        print(f"{symbol} {test_name}: {status}")
        
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal Tests: {len(results)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 All tests passed! Your installation is ready.")
        print("\nNext steps:")
        print("1. Review and customize genai_config.json")
        print("2. Add your test files to the test_suite directory")
        print("3. Run: python btp.py")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please address the issues above.")
        print("\nCommon solutions:")
        print("- Set GEMINI_API_KEY environment variable")
        print("- Install missing dependencies: pip install -r requirements.txt")
        print("- Check network connectivity for API access")
    
    return failed == 0

if __name__ == "__main__":
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\nUnexpected error: {e}")
        sys.exit(1)
