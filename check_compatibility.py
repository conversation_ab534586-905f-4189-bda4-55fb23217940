#!/usr/bin/env python3
"""
Check compatibility with Google Generative AI library
Tests which features are available in the current version
"""

import os
import google.generativeai as genai
from dotenv import load_dotenv

load_dotenv()

def check_model_availability():
    """Check which models are available"""
    print("🔍 Checking available models...")
    
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ GEMINI_API_KEY not found")
        return False
    
    try:
        genai.configure(api_key=api_key)
        
        # List available models
        models = genai.list_models()
        available_models = []
        
        for model in models:
            if hasattr(model, 'name'):
                available_models.append(model.name)
                print(f"✅ Available: {model.name}")
        
        # Check for specific models we want
        target_models = ['gemini-2.5-pro', 'gemini-1.5-pro', 'gemini-1.5-flash']
        for target in target_models:
            found = any(target in model for model in available_models)
            if found:
                print(f"✅ Target model {target} is available")
            else:
                print(f"❌ Target model {target} is NOT available")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking models: {e}")
        return False

def check_generation_config_features():
    """Check which GenerationConfig features are supported"""
    print("\n🔧 Checking GenerationConfig features...")
    
    try:
        # Test basic config
        basic_config = genai.types.GenerationConfig(
            temperature=0.2,
            max_output_tokens=1000
        )
        print("✅ Basic GenerationConfig works")
        
        # Test advanced features
        try:
            advanced_config = genai.types.GenerationConfig(
                temperature=0.2,
                max_output_tokens=1000,
                top_p=0.95,
                top_k=64,
                candidate_count=1
            )
            print("✅ Advanced GenerationConfig features work")
        except Exception as e:
            print(f"❌ Advanced GenerationConfig failed: {e}")
        
        # Test response_mime_type
        try:
            mime_config = genai.types.GenerationConfig(
                temperature=0.2,
                max_output_tokens=1000,
                response_mime_type="application/json"
            )
            print("✅ response_mime_type is supported")
        except Exception as e:
            print(f"❌ response_mime_type not supported: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ GenerationConfig test failed: {e}")
        return False

def check_safety_settings():
    """Check which safety settings are supported"""
    print("\n🛡️ Checking safety settings...")
    
    standard_categories = [
        "HARM_CATEGORY_HARASSMENT",
        "HARM_CATEGORY_HATE_SPEECH", 
        "HARM_CATEGORY_SEXUALLY_EXPLICIT",
        "HARM_CATEGORY_DANGEROUS_CONTENT"
    ]
    
    additional_categories = [
        "HARM_CATEGORY_CIVIC_INTEGRITY"
    ]
    
    working_categories = []
    
    # Test standard categories
    for category in standard_categories:
        try:
            safety_setting = {"category": category, "threshold": "BLOCK_NONE"}
            working_categories.append(safety_setting)
            print(f"✅ {category} is supported")
        except Exception as e:
            print(f"❌ {category} failed: {e}")
    
    # Test additional categories
    for category in additional_categories:
        try:
            safety_setting = {"category": category, "threshold": "BLOCK_NONE"}
            # Try to create a model with this setting to test
            test_config = genai.types.GenerationConfig(temperature=0.2, max_output_tokens=100)
            test_model = genai.GenerativeModel(
                model_name="gemini-1.5-flash",  # Use a basic model for testing
                generation_config=test_config,
                safety_settings=[safety_setting]
            )
            working_categories.append(safety_setting)
            print(f"✅ {category} is supported")
        except Exception as e:
            print(f"❌ {category} not supported: {e}")
    
    return working_categories

def test_basic_generation():
    """Test basic text generation"""
    print("\n🧪 Testing basic generation...")
    
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ GEMINI_API_KEY not found")
        return False
    
    try:
        genai.configure(api_key=api_key)
        
        # Try with the most basic configuration
        model = genai.GenerativeModel('gemini-2.5-pro')
        
        response = model.generate_content("Say 'Hello, compatibility test successful!'")
        
        if hasattr(response, 'text') and response.text:
            print(f"✅ Basic generation works: {response.text}")
            return True
        else:
            print("❌ No text in response")
            return False
            
    except Exception as e:
        print(f"❌ Basic generation failed: {e}")
        
        # Try fallback model
        try:
            print("🔄 Trying fallback model...")
            model = genai.GenerativeModel('gemini-1.5-pro')
            response = model.generate_content("Say 'Hello from fallback model!'")
            
            if hasattr(response, 'text') and response.text:
                print(f"✅ Fallback model works: {response.text}")
                return True
            else:
                print("❌ Fallback model also failed")
                return False
                
        except Exception as e2:
            print(f"❌ Fallback model failed: {e2}")
            return False

def main():
    """Run all compatibility checks"""
    print("=" * 60)
    print("Google Generative AI Compatibility Check")
    print("=" * 60)
    
    results = []
    
    # Run tests
    tests = [
        ("Model Availability", check_model_availability),
        ("GenerationConfig Features", check_generation_config_features),
        ("Safety Settings", check_safety_settings),
        ("Basic Generation", test_basic_generation)
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"Running: {test_name}")
        print(f"{'-' * 40}")
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'=' * 60}")
    print("COMPATIBILITY CHECK SUMMARY")
    print(f"{'=' * 60}")
    
    passed = 0
    for test_name, result in results:
        if isinstance(result, bool):
            status = "PASS" if result else "FAIL"
            symbol = "✅" if result else "❌"
            if result:
                passed += 1
        else:
            status = "INFO"
            symbol = "ℹ️"
        
        print(f"{symbol} {test_name}: {status}")
    
    print(f"\nCompatibility Score: {passed}/{len([r for r in results if isinstance(r[1], bool)])}")
    
    if passed == len([r for r in results if isinstance(r[1], bool)]):
        print("\n🎉 Full compatibility confirmed!")
    else:
        print("\n⚠️ Some features may not be available. Check the details above.")
        print("\nRecommendations:")
        print("1. Update google-generativeai: pip install --upgrade google-generativeai")
        print("2. Check your API key has access to the models you want to use")
        print("3. Use the working features identified above")

if __name__ == "__main__":
    main()
