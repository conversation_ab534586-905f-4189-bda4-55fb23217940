#!/usr/bin/env python3
"""
JSON Parsing Fix Utility
Adds robust JSON parsing and fallback mechanisms
"""

import json
import re
import logging

logger = logging.getLogger(__name__)

def clean_and_parse_json(response_text: str, fallback_data: dict = None) -> dict:
    """
    Robust JSON parsing with multiple fallback strategies
    """
    if not response_text or not response_text.strip():
        logger.warning("Empty response text")
        return fallback_data or {}
    
    text = response_text.strip()
    
    # Strategy 1: Direct parsing
    try:
        return json.loads(text)
    except json.JSONDecodeError:
        pass
    
    # Strategy 2: Remove markdown formatting
    if '```' in text:
        # Remove code blocks
        text = re.sub(r'```(?:json)?\s*', '', text)
        text = re.sub(r'```\s*', '', text)
        text = text.strip()
        
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass
    
    # Strategy 3: Extract JSON from mixed content
    json_patterns = [
        r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}',  # Simple nested JSON
        r'\{.*?\}',  # Any content between braces
    ]
    
    for pattern in json_patterns:
        matches = re.findall(pattern, text, re.DOTALL)
        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue
    
    # Strategy 4: Fix common JSON issues
    fixes = [
        # Fix single quotes to double quotes
        (r"'([^']*)':", r'"\1":'),
        # Fix unquoted keys
        (r'(\w+):', r'"\1":'),
        # Fix trailing commas
        (r',\s*}', '}'),
        (r',\s*]', ']'),
    ]
    
    fixed_text = text
    for pattern, replacement in fixes:
        fixed_text = re.sub(pattern, replacement, fixed_text)
    
    try:
        return json.loads(fixed_text)
    except json.JSONDecodeError:
        pass
    
    # Strategy 5: Extract key-value pairs manually
    try:
        result = {}
        
        # Look for key-value patterns
        kv_patterns = [
            r'"([^"]+)":\s*"([^"]*)"',  # "key": "value"
            r'"([^"]+)":\s*(\d+\.?\d*)',  # "key": number
            r'"([^"]+)":\s*\[([^\]]*)\]',  # "key": [array]
            r'"([^"]+)":\s*(true|false)',  # "key": boolean
        ]
        
        for pattern in kv_patterns:
            matches = re.findall(pattern, text)
            for key, value in matches:
                if value in ['true', 'false']:
                    result[key] = value == 'true'
                elif value.replace('.', '').isdigit():
                    result[key] = float(value) if '.' in value else int(value)
                elif value.startswith('[') and value.endswith(']'):
                    # Simple array parsing
                    array_content = value[1:-1].strip()
                    if array_content:
                        items = [item.strip().strip('"') for item in array_content.split(',')]
                        result[key] = items
                    else:
                        result[key] = []
                else:
                    result[key] = value
        
        if result:
            return result
    except Exception as e:
        logger.warning(f"Manual parsing failed: {e}")
    
    # Final fallback
    logger.error(f"All JSON parsing strategies failed for: {text[:200]}...")
    return fallback_data or {}

def get_test_analysis_fallback(file_path: str) -> dict:
    """Generate fallback test analysis based on filename"""
    filename = file_path.lower()
    
    # Determine test type from filename
    if 'stress' in filename:
        test_type = 'stress_test'
        complexity = 'high'
        bug_potential = 'high'
        execution_time = 120.0
    elif 'corner' in filename or 'edge' in filename:
        test_type = 'corner_case'
        complexity = 'medium'
        bug_potential = 'high'
        execution_time = 45.0
    elif 'integration' in filename or 'interface' in filename:
        test_type = 'integration_test'
        complexity = 'high'
        bug_potential = 'medium'
        execution_time = 60.0
    elif 'basic' in filename or 'simple' in filename:
        test_type = 'unit_test'
        complexity = 'low'
        bug_potential = 'medium'
        execution_time = 20.0
    else:
        test_type = 'unit_test'
        complexity = 'medium'
        bug_potential = 'medium'
        execution_time = 30.0
    
    # Determine functional areas from filename
    functional_areas = []
    if 'alu' in filename:
        functional_areas.extend(['alu', 'arithmetic'])
    if 'memory' in filename:
        functional_areas.extend(['memory', 'interface'])
    if 'interrupt' in filename:
        functional_areas.extend(['interrupt', 'control'])
    if 'cpu' in filename:
        functional_areas.extend(['cpu', 'processor'])
    
    if not functional_areas:
        functional_areas = ['unknown']
    
    return {
        "test_type": test_type,
        "complexity_level": complexity,
        "parameters": {},
        "estimated_execution_time": execution_time,
        "functional_areas": functional_areas,
        "bug_detection_potential": bug_potential,
        "coverage_areas": functional_areas,
        "dependencies": [],
        "uniqueness_indicators": [test_type],
        "summary": f"Fallback analysis for {filename}"
    }

# Test the parsing function
if __name__ == "__main__":
    # Test cases
    test_responses = [
        '{"test_type": "unit_test", "complexity": "low"}',
        '```json\n{"test_type": "unit_test"}\n```',
        'Here is the analysis: {"test_type": "unit_test"} Hope this helps!',
        "{'test_type': 'unit_test', 'complexity': 'low'}",
        'test_type: "unit_test"\ncomplexity: "low"',
        '',
        'Invalid response with no JSON',
    ]
    
    print("Testing JSON parsing fixes:")
    for i, response in enumerate(test_responses):
        print(f"\nTest {i+1}: {response[:50]}...")
        result = clean_and_parse_json(response, {"fallback": True})
        print(f"Result: {result}")
